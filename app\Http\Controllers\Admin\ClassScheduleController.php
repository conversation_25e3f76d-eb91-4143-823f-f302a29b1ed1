<?php

namespace App\Http\Controllers\Admin;

use App\Models\Classroom;
use App\Models\LessonHour;
use App\Models\AcademicYear;
use Illuminate\Http\Request;
use App\Models\ClassSchedule;
use App\Models\TeacherAssignment;
use Illuminate\Http\JsonResponse;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use App\Http\Requests\ClassScheduleRequests\ClassScheduleStoreRequest;
use App\Http\Requests\ClassScheduleRequests\ClassScheduleUpdateRequest;

class ClassScheduleController extends Controller
{
    /**
     * Display a listing of class schedules by classroom, academic year
     */
    public function index(Request $request): View
    {
        $classrooms = Classroom::where('status', 'active')->get();
        $academicYears = AcademicYear::where('status', 'active')->get();
        return view('admin.pages.class-schedule.index-vue', [
            'classrooms' => $classrooms,
            'academicYears' => $academicYears,
        ]);
    }

    /**
     * Display the improved scheduling interface.
     */
    public function indexImproved(Request $request): View
    {
        $classrooms = Classroom::with(['teacher.user', 'program', 'shift', 'academicYear'])
            ->where('status', 'active')
            ->orderBy('name')
            ->get();

        $academicYears = AcademicYear::orderBy('start_date', 'desc')->get();

        return view('admin.pages.class-schedule.index-improved', compact('classrooms', 'academicYears'));
    }

    /**
     * Show the form for creating a new class schedule
     */
    public function create(Request $request): View|RedirectResponse
    {
        $classroomId = $request->input('classroom_id');
        $academicYearId = $request->input('academic_year_id');

        if (!$classroomId || !$academicYearId) {
            return redirect()->route('admin.class-schedules.index')
                ->with('error', 'Pilih kelas dan tahun akademik terlebih dahulu');
        }

        $selectedClassroom = Classroom::with('shift')->findOrFail($classroomId);
        $selectedAcademicYear = AcademicYear::findOrFail($academicYearId);
        $teacherAssignments = TeacherAssignment::where('classroom_id', $classroomId)
            ->where('academic_year_id', $academicYearId)
            ->get();
        $lessonHours = LessonHour::where('classroom_id', $classroomId)->get();

        return view('admin.pages.class-schedule.create', [
            'teacherAssignments' => $teacherAssignments,
            'lessonHours' => $lessonHours,
            'days' => config('constants.days'),
            'selectedClassroom' => $selectedClassroom,
            'selectedAcademicYear' => $selectedAcademicYear,
        ]);
    }

    /**
     * Store a newly created class schedule
     */
    public function store(ClassScheduleStoreRequest $request): JsonResponse
    {
        try {
            $classSchedule = ClassSchedule::create($request->validated());
            return response()->json([
                'success' => true,
                'message' => 'Jadwal kelas berhasil disimpan',
                'data' => $classSchedule,
            ]);
        } catch (\Exception $e) {
            Log::error('Error creating class schedule: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menyimpan jadwal kelas',
            ], 500);
        }
    }

    /**
     * Show the form for editing the specified class schedule
     */
    public function edit(ClassSchedule $classSchedule): View
    {
        $teacherAssignment = $classSchedule->teacherAssignment;
        $classroom = $teacherAssignment->classroom;
        $classroom->load('shift');
        $academicYear = $teacherAssignment->academicYear;
        $teacherAssignments = TeacherAssignment::where('classroom_id', $classroom->id)
            ->where('academic_year_id', $academicYear->id)
            ->get();
        $lessonHours = LessonHour::where('classroom_id', $classroom->id)->get();

        return view('admin.pages.class-schedule.edit', [
            'classSchedule' => $classSchedule,
            'teacherAssignments' => $teacherAssignments,
            'lessonHours' => $lessonHours,
            'days' => config('constants.days'),
            'selectedClassroom' => $classroom,
            'selectedAcademicYear' => $academicYear,
        ]);
    }

    /**
     * Update the specified class schedule
     */
    public function update(ClassScheduleUpdateRequest $request, ClassSchedule $classSchedule): JsonResponse
    {
        try {
            $updated = $classSchedule->update($request->validated());
            return response()->json([
                'success' => true,
                'message' => $updated ? 'Jadwal kelas berhasil diperbarui' : 'Tidak ada perubahan data',
            ]);
        } catch (\Exception $e) {
            Log::error('Error updating class schedule: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat memperbarui jadwal kelas',
            ], 500);
        }
    }

    /**
     * Remove the specified class schedule
     */
    public function destroy(ClassSchedule $classSchedule): JsonResponse
    {
        try {
            $classSchedule->delete();
            return response()->json([
                'success' => true,
                'message' => 'Jadwal kelas berhasil dihapus',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 422);
        }
    }
}
