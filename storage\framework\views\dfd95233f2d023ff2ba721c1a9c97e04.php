<?php $__env->startSection('title', 'Edit Jam Pelajaran'); ?>

<?php $__env->startSection('content'); ?>
    <?php echo $__env->make('admin.components.page-title', [
        'title' => 'Edit Jam Pelajaran',
        'breadcrumb' => 'Manajemen Kelas',
    ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header border-bottom-dashed">
                    <div class="d-flex align-items-center">
                        <h5 class="card-title mb-0 flex-grow-1">
                            <i class="ri-time-line text-muted me-1"></i> Edit Jam Pelajaran: <?php echo e($lessonHour->name); ?>

                        </h5>
                        <div class="flex-shrink-0">
                            <a href="<?php echo e(route('admin.lesson-hours.index')); ?>" class="btn btn-soft-danger">
                                <i class="ri-arrow-left-line align-bottom"></i> Kembali
                            </a>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <div id="lesson-hour-error-msg" class="alert alert-danger py-2 d-none"></div>

                    <form id="edit-lesson-hour-form" action="<?php echo e(route('admin.lesson-hours.update', $lessonHour->id)); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>

                        <div class="row g-3">
                            <div class="col-lg-8 mx-auto">
                                <!-- Lesson Hour Name -->
                                <div class="mb-3">
                                    <label for="name" class="form-label">Nama Jam Pelajaran <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" value="<?php echo e($lessonHour->name); ?>" required placeholder="Contoh: Jam 1, Jam ke-1, dll">
                                </div>

                                <div class="row">
                                    <!-- Start Time -->
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="start_time" class="form-label">Waktu Mulai <span class="text-danger">*</span></label>
                                            <input type="time" class="form-control" id="start_time" name="start_time" value="<?php echo e($lessonHour->start_time); ?>" required>
                                        </div>
                                    </div>

                                    <!-- End Time -->
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="end_time" class="form-label">Waktu Selesai <span class="text-danger">*</span></label>
                                            <input type="time" class="form-control" id="end_time" name="end_time" value="<?php echo e($lessonHour->end_time); ?>" required>
                                        </div>
                                    </div>
                                </div>

                                <!-- Sequence -->
                                <div class="mb-3">
                                    <label for="sequence" class="form-label">Urutan <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="sequence" name="sequence" value="<?php echo e($lessonHour->sequence); ?>" required min="1">
                                    <small class="text-muted">Urutan akan menentukan posisi jam pelajaran dalam jadwal</small>
                                </div>

                                <!-- Classroom -->
                                <div class="mb-3">
                                    <label for="classroom_id" class="form-label">Kelas</label>
                                    <select class="form-select" data-choices id="classroom_id" name="classroom_id">
                                        <option value="">Pilih Kelas (Opsional)</option>
                                        <?php $__currentLoopData = $classrooms; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $classroom): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($classroom->id); ?>" <?php echo e($lessonHour->classroom_id == $classroom->id ? 'selected' : ''); ?>>
                                                <?php echo e($classroom->name); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                    <small class="text-muted">Jika tidak dipilih, jam pelajaran akan berlaku untuk semua kelas (global)</small>
                                </div>

                                <!-- Shift -->
                                <div class="mb-3">
                                    <label for="shift_id" class="form-label">Shift</label>
                                    <select class="form-select" data-choices id="shift_id" name="shift_id">
                                        <option value="">Pilih Shift (Opsional)</option>
                                        <?php $__currentLoopData = $shifts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $shift): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($shift->id); ?>" <?php echo e($lessonHour->shift_id == $shift->id ? 'selected' : ''); ?>>
                                                <?php echo e($shift->name); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                    <small class="text-muted">Jika tidak dipilih, jam pelajaran akan berlaku untuk semua shift (global)</small>
                                </div>

                                <div class="d-flex justify-content-end gap-2 mt-4">
                                    <a href="<?php echo e(route('admin.lesson-hours.index')); ?>" class="btn btn-ghost-secondary">
                                        <i class="ri-close-line align-bottom"></i> Batal
                                    </a>
                                    <button type="button" class="btn btn-primary" id="btn-update-lesson-hour">
                                        <i class="ri-save-line align-bottom"></i> Simpan Perubahan
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.partials.plugins.jquery', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php echo $__env->make('admin.partials.plugins.sweetalert', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

<?php $__env->startPush('scripts'); ?>
    <script type="text/javascript">
        $(document).ready(function() {
            // Update lesson hour handler
            $('#btn-update-lesson-hour').on('click', function() {
                handleUpdateLessonHour(this);
            });

            // Time validation
            $('#end_time').on('change', function() {
                validateTimes();
            });

            $('#start_time').on('change', function() {
                validateTimes();
            });
        });

        function validateTimes() {
            const startTime = $('#start_time').val();
            const endTime = $('#end_time').val();

            if (startTime && endTime && startTime >= endTime) {
                $('#lesson-hour-error-msg').text('Waktu selesai harus lebih besar dari waktu mulai').removeClass('d-none');
                $('#end_time').addClass('is-invalid');
                return false;
            } else {
                $('#lesson-hour-error-msg').addClass('d-none');
                $('#end_time').removeClass('is-invalid');
                return true;
            }
        }

        async function handleUpdateLessonHour(buttonElement) {
            let originalBtnContent = '';

            try {
                const form = document.getElementById('edit-lesson-hour-form');
                const errorMsg = document.getElementById('lesson-hour-error-msg');

                if (!form || !errorMsg || !buttonElement) {
                    console.error('Element not found!');
                    return;
                }

                errorMsg.classList.add('d-none');

                if (!validateTimes()) {
                    return;
                }

                if (!form.checkValidity()) {
                    form.reportValidity();
                    return;
                }

                // Save button content before modification
                originalBtnContent = buttonElement.innerHTML;
                buttonElement.disabled = true;
                buttonElement.innerHTML = '<i class="ri-loader-4-line align-bottom animate-spin"></i> Memproses...';

                const formData = new FormData(form);
                const response = await fetch(form.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'Accept': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    }
                });

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.message || 'Gagal memperbarui jam pelajaran');
                }

                await Swal.fire({
                    title: 'Berhasil!',
                    text: data.message,
                    icon: 'success',
                    showConfirmButton: false,
                    timer: 2000
                });

                // Redirect to lesson hour list after successful update
                setTimeout(() => {
                    window.location.href = "<?php echo e(route('admin.lesson-hours.index')); ?>";
                }, 1000);

            } catch (error) {
                console.error('Error:', error);

                const errorMsg = document.getElementById('lesson-hour-error-msg');
                if (errorMsg) {
                    errorMsg.textContent = error.message;
                    errorMsg.classList.remove('d-none');
                    errorMsg.scrollIntoView({
                        behavior: 'smooth',
                        block: 'center'
                    });
                } else {
                    Swal.fire({
                        title: 'Error!',
                        text: error.message,
                        icon: 'error'
                    });
                }
            } finally {
                // Restore button state
                if (buttonElement) {
                    buttonElement.disabled = false;
                    buttonElement.innerHTML = originalBtnContent || '<i class="ri-save-line align-bottom"></i> Simpan Perubahan';
                }
            }
        }
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\rawooh-mvc\resources\views/admin/pages/lesson-hour/edit.blade.php ENDPATH**/ ?>