@extends('admin.layouts.app')

@section('title', 'Tambah Penugasan Guru')

@section('content')
    @include('admin.components.page-title', [
        'title' => 'Tambah Penugasan Guru',
        'breadcrumb' => 'Manajemen Akademik',
        'breadcrumb_items' => [
            [
                'text' => 'Penugasan Guru',
                'url' => route('admin.teacher-assignments.index'),
            ],
            [
                'text' => 'Tambah Penugasan Guru',
                'url' => null,
            ],
        ],
    ])

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Tambah Penugasan Guru</h5>
                </div>
                <div class="card-body">
                    <form id="create-teacher-assignment-form" action="{{ route('admin.teacher-assignments.store') }}" method="POST">
                        @csrf

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="teacher_id" class="form-label">Guru <span class="text-danger">*</span></label>
                                <select class="form-select" data-choices id="teacher_id" name="teacher_id" required>
                                    <option value="">Pilih Guru</option>
                                    @foreach ($teachers as $teacher)
                                        <option value="{{ $teacher->id }}">{{ $teacher->user->name }}</option>
                                    @endforeach
                                </select>
                                <div class="invalid-feedback" data-field="teacher_id"></div>
                            </div>
                            <div class="col-md-6">
                                <label for="classroom_id" class="form-label">Kelas <span class="text-danger">*</span></label>
                                <select class="form-select" data-choices id="classroom_id" name="classroom_id" required>
                                    <option value="">Pilih Kelas</option>
                                    @foreach ($classrooms as $classroom)
                                        <option value="{{ $classroom->id }}">{{ $classroom->name }}</option>
                                    @endforeach
                                </select>
                                <div class="invalid-feedback" data-field="classroom_id"></div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="subject_id" class="form-label">Mata Pelajaran</label>
                                <select class="form-select" data-choices id="subject_id" name="subject_id">
                                    <option value="">Pilih Mata Pelajaran</option>
                                    @foreach ($subjects as $subject)
                                        <option value="{{ $subject->id }}">{{ $subject->name }}</option>
                                    @endforeach
                                </select>
                                <div class="invalid-feedback" data-field="subject_id"></div>
                            </div>
                            <div class="col-md-6">
                                <label for="academic_year_id" class="form-label">Tahun Akademik <span class="text-danger">*</span></label>
                                <select class="form-select" data-choices id="academic_year_id" name="academic_year_id" required>
                                    <option value="">Pilih Tahun Akademik</option>
                                    @foreach ($academicYears as $academicYear)
                                        <option value="{{ $academicYear->id }}">{{ $academicYear->name }}</option>
                                    @endforeach
                                </select>
                                <div class="invalid-feedback" data-field="academic_year_id"></div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="is_homeroom_teacher" class="form-label">Jenis Penugasan</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_homeroom_teacher" name="is_homeroom_teacher" value="1">
                                    <label class="form-check-label" for="is_homeroom_teacher">
                                        Jadikan sebagai Wali Kelas
                                    </label>
                                    <div class="form-text">Centang jika guru akan menjadi wali kelas untuk kelas ini</div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary" id="submit-btn">
                                <i class="ri-save-line align-bottom me-1"></i> Simpan
                            </button>
                            <a href="{{ route('admin.teacher-assignments.index') }}" class="btn btn-light">
                                <i class="ri-arrow-left-line align-bottom me-1"></i> Kembali
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Informasi</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info mb-0">
                        <h6 class="alert-heading">Petunjuk:</h6>
                        <ul class="mb-0">
                            <li>Pilih guru yang akan ditugaskan</li>
                            <li>Pilih kelas yang akan diajar</li>
                            <li>Pilih mata pelajaran (opsional untuk wali kelas)</li>
                            <li>Pilih tahun akademik</li>
                            <li>Centang "Wali Kelas" jika guru akan menjadi wali kelas</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@include('admin.partials.plugins.jquery')
@include('admin.partials.plugins.sweetalert')

@push('scripts')
    <script type="text/javascript">
        $(document).ready(function() {
            $('#create-teacher-assignment-form').on('submit', function(e) {
                e.preventDefault();

                const $form = $(this);
                const $submitBtn = $('#submit-btn');
                const originalText = $submitBtn.html();

                // Reset validation
                $('.is-invalid').removeClass('is-invalid');
                $('.invalid-feedback').text('');

                // Disable submit button
                $submitBtn.prop('disabled', true).html('<i class="ri-loader-line spin"></i> Menyimpan...');

                $.ajax({
                    url: $form.attr('action'),
                    type: 'POST',
                    data: $form.serialize(),
                    success: function(response) {
                        Swal.fire({
                            title: 'Berhasil!',
                            text: response.message,
                            icon: 'success',
                            showConfirmButton: false,
                            timer: 1500,
                        }).then(() => {
                            window.location.href = '{{ route('admin.teacher-assignments.index') }}';
                        });
                    },
                    error: function(xhr) {
                        const response = xhr.responseJSON;

                        if (response.errors) {
                            // Display validation errors
                            Object.keys(response.errors).forEach(field => {
                                const $field = $(`[name="${field}"]`);
                                const $feedback = $(`[data-field="${field}"]`);

                                $field.addClass('is-invalid');
                                $feedback.text(response.errors[field][0]);
                            });
                        } else {
                            // Display general error
                            Swal.fire({
                                title: 'Error!',
                                text: response.message || 'Terjadi kesalahan saat menyimpan data.',
                                icon: 'error',
                            });
                        }
                    },
                    complete: function() {
                        // Re-enable submit button
                        $submitBtn.prop('disabled', false).html(originalText);
                    }
                });
            });
        });
    </script>
@endpush
