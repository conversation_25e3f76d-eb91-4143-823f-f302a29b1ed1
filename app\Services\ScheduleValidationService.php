<?php

namespace App\Services;

use App\Models\ClassSchedule;
use App\Models\TeacherAssignment;
use App\Models\LessonHour;
use Illuminate\Support\Collection;

class ScheduleValidationService
{
    /**
     * Validate a schedule for conflicts
     */
    public function validateSchedule(
        int $teacherAssignmentId,
        int $lessonHourId,
        string $dayOfWeek,
        ?int $excludeScheduleId = null
    ): array {
        $conflicts = [];

        // Get teacher assignment details
        $teacherAssignment = TeacherAssignment::with(['teacher.user', 'classroom', 'academicYear', 'subject'])
            ->find($teacherAssignmentId);

        if (!$teacherAssignment) {
            return [
                [
                    'type' => 'invalid_assignment',
                    'message' => 'Penugasan guru tidak ditemukan',
                    'severity' => 'error'
                ]
            ];
        }

        // Get lesson hour details
        $lessonHour = LessonHour::find($lessonHourId);
        if (!$lessonHour) {
            return [
                [
                    'type' => 'invalid_lesson_hour',
                    'message' => 'Jam pelajaran tidak ditemukan',
                    'severity' => 'error'
                ]
            ];
        }

        // Check teacher conflicts
        $teacherConflicts = $this->checkTeacherConflicts(
            $teacherAssignment,
            $lessonHour,
            $dayOfWeek,
            $excludeScheduleId
        );
        $conflicts = array_merge($conflicts, $teacherConflicts);

        // Check classroom conflicts
        $classroomConflicts = $this->checkClassroomConflicts(
            $teacherAssignment,
            $lessonHour,
            $dayOfWeek,
            $excludeScheduleId
        );
        $conflicts = array_merge($conflicts, $classroomConflicts);

        // Check lesson hour availability
        $lessonHourConflicts = $this->checkLessonHourConflicts(
            $teacherAssignment,
            $lessonHour,
            $dayOfWeek
        );
        $conflicts = array_merge($conflicts, $lessonHourConflicts);

        return $conflicts;
    }

    /**
     * Check for teacher scheduling conflicts
     */
    private function checkTeacherConflicts(
        TeacherAssignment $teacherAssignment,
        LessonHour $lessonHour,
        string $dayOfWeek,
        ?int $excludeScheduleId = null
    ): array {
        $conflicts = [];

        // Check if teacher has another class at the same time
        $conflictingSchedules = ClassSchedule::with(['teacherAssignment.classroom', 'teacherAssignment.subject', 'lessonHour'])
            ->whereHas('teacherAssignment', function ($query) use ($teacherAssignment) {
                $query->where('teacher_id', $teacherAssignment->teacher_id)
                      ->where('academic_year_id', $teacherAssignment->academic_year_id);
            })
            ->where('lesson_hour_id', $lessonHour->id)
            ->where('day_of_week', $dayOfWeek)
            ->where('status', 'active')
            ->when($excludeScheduleId, function ($query) use ($excludeScheduleId) {
                $query->where('id', '!=', $excludeScheduleId);
            })
            ->get();

        foreach ($conflictingSchedules as $schedule) {
            $conflicts[] = [
                'type' => 'teacher_conflict',
                'message' => 'Guru sudah mengajar di kelas lain pada jam yang sama',
                'severity' => 'error',
                'details' => [
                    'teacher' => $teacherAssignment->teacher->user->name,
                    'conflicting_classroom' => $schedule->teacherAssignment->classroom->name,
                    'conflicting_subject' => $schedule->teacherAssignment->subject->name,
                    'lesson_hour' => $schedule->lessonHour->name,
                    'time' => $schedule->lessonHour->formatted_start_time . ' - ' . $schedule->lessonHour->formatted_end_time,
                    'day' => $dayOfWeek
                ]
            ];
        }

        return $conflicts;
    }

    /**
     * Check for classroom scheduling conflicts
     */
    private function checkClassroomConflicts(
        TeacherAssignment $teacherAssignment,
        LessonHour $lessonHour,
        string $dayOfWeek,
        ?int $excludeScheduleId = null
    ): array {
        $conflicts = [];

        // Check if classroom already has a schedule at the same time
        $conflictingSchedules = ClassSchedule::with(['teacherAssignment.teacher.user', 'teacherAssignment.subject', 'lessonHour'])
            ->whereHas('teacherAssignment', function ($query) use ($teacherAssignment) {
                $query->where('classroom_id', $teacherAssignment->classroom_id)
                      ->where('academic_year_id', $teacherAssignment->academic_year_id);
            })
            ->where('lesson_hour_id', $lessonHour->id)
            ->where('day_of_week', $dayOfWeek)
            ->where('status', 'active')
            ->when($excludeScheduleId, function ($query) use ($excludeScheduleId) {
                $query->where('id', '!=', $excludeScheduleId);
            })
            ->get();

        foreach ($conflictingSchedules as $schedule) {
            $conflicts[] = [
                'type' => 'classroom_conflict',
                'message' => 'Kelas sudah memiliki jadwal pada jam yang sama',
                'severity' => 'error',
                'details' => [
                    'classroom' => $teacherAssignment->classroom->name,
                    'conflicting_teacher' => $schedule->teacherAssignment->teacher->user->name,
                    'conflicting_subject' => $schedule->teacherAssignment->subject->name,
                    'lesson_hour' => $schedule->lessonHour->name,
                    'time' => $schedule->lessonHour->formatted_start_time . ' - ' . $schedule->lessonHour->formatted_end_time,
                    'day' => $dayOfWeek
                ]
            ];
        }

        return $conflicts;
    }

    /**
     * Check for lesson hour specific conflicts
     */
    private function checkLessonHourConflicts(
        TeacherAssignment $teacherAssignment,
        LessonHour $lessonHour,
        string $dayOfWeek
    ): array {
        $conflicts = [];

        // Check if lesson hour belongs to the same classroom
        if ($lessonHour->classroom_id && $lessonHour->classroom_id !== $teacherAssignment->classroom_id) {
            $conflicts[] = [
                'type' => 'lesson_hour_mismatch',
                'message' => 'Jam pelajaran tidak sesuai dengan kelas',
                'severity' => 'warning',
                'details' => [
                    'lesson_hour_classroom' => $lessonHour->classroom->name ?? 'Unknown',
                    'assignment_classroom' => $teacherAssignment->classroom->name,
                    'lesson_hour' => $lessonHour->name
                ]
            ];
        }

        // Check if lesson hour is during break time (optional validation)
        if (str_contains(strtolower($lessonHour->name), 'istirahat') || str_contains(strtolower($lessonHour->name), 'break')) {
            $conflicts[] = [
                'type' => 'break_time_schedule',
                'message' => 'Tidak dapat menjadwalkan pelajaran pada jam istirahat',
                'severity' => 'warning',
                'details' => [
                    'lesson_hour' => $lessonHour->name,
                    'time' => $lessonHour->formatted_start_time . ' - ' . $lessonHour->formatted_end_time
                ]
            ];
        }

        return $conflicts;
    }

    /**
     * Validate multiple schedules for bulk operations
     */
    public function validateBulkSchedules(array $schedules): array
    {
        $results = [];
        $allConflicts = [];

        foreach ($schedules as $index => $schedule) {
            $conflicts = $this->validateSchedule(
                $schedule['teacher_assignment_id'],
                $schedule['lesson_hour_id'],
                $schedule['day_of_week'],
                $schedule['exclude_schedule_id'] ?? null
            );

            $results[$index] = [
                'has_conflicts' => !empty($conflicts),
                'conflicts' => $conflicts
            ];

            if (!empty($conflicts)) {
                $allConflicts[$index] = $conflicts;
            }
        }

        return [
            'individual_results' => $results,
            'has_any_conflicts' => !empty($allConflicts),
            'all_conflicts' => $allConflicts
        ];
    }

    /**
     * Get schedule recommendations based on teacher availability
     */
    public function getScheduleRecommendations(
        int $teacherAssignmentId,
        string $dayOfWeek,
        ?int $excludeScheduleId = null
    ): array {
        $teacherAssignment = TeacherAssignment::with(['classroom', 'academicYear'])
            ->find($teacherAssignmentId);

        if (!$teacherAssignment) {
            return [];
        }

        // Get available lesson hours for the classroom
        $availableLessonHours = LessonHour::where('classroom_id', $teacherAssignment->classroom_id)
            ->orderBy('sequence')
            ->get();

        $recommendations = [];

        foreach ($availableLessonHours as $lessonHour) {
            $conflicts = $this->validateSchedule(
                $teacherAssignmentId,
                $lessonHour->id,
                $dayOfWeek,
                $excludeScheduleId
            );

            $recommendations[] = [
                'lesson_hour' => $lessonHour,
                'has_conflicts' => !empty($conflicts),
                'conflicts' => $conflicts,
                'recommendation_score' => $this->calculateRecommendationScore($conflicts)
            ];
        }

        // Sort by recommendation score (higher is better)
        usort($recommendations, function ($a, $b) {
            return $b['recommendation_score'] <=> $a['recommendation_score'];
        });

        return $recommendations;
    }

    /**
     * Calculate recommendation score based on conflicts
     */
    private function calculateRecommendationScore(array $conflicts): int
    {
        if (empty($conflicts)) {
            return 100; // Perfect score for no conflicts
        }

        $score = 100;
        foreach ($conflicts as $conflict) {
            switch ($conflict['severity']) {
                case 'error':
                    $score -= 50;
                    break;
                case 'warning':
                    $score -= 20;
                    break;
                default:
                    $score -= 10;
                    break;
            }
        }

        return max(0, $score);
    }
}
