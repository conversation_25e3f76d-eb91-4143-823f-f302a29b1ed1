<?php

namespace App\Http\Requests\TeacherAssignmentRequests;

use Illuminate\Foundation\Http\FormRequest;

class TeacherAssignmentFilterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'teacher_id' => ['sometimes', 'nullable', 'integer'],
            'subject_id' => ['sometimes', 'nullable', 'integer'],
            'classroom_id' => ['sometimes', 'nullable', 'integer'],
            'academic_year_id' => ['sometimes', 'nullable', 'integer'],
            'is_homeroom_teacher' => ['sometimes', 'nullable', 'boolean'],
            'search' => ['sometimes', 'nullable', 'string', 'max:255'],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'teacher_id' => 'Guru',
            'subject_id' => '<PERSON>',
            'classroom_id' => 'Kelas',
            'academic_year_id' => 'Tahun Akademik',
            'is_homeroom_teacher' => '<PERSON><PERSON>',
            'status' => 'Status',
            'search' => 'Pencarian',
        ];
    }
}
