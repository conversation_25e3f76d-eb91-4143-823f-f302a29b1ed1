<?php

namespace App\Services;

use App\Models\ClassSchedule;
use App\Models\TeacherAssignment;
use App\Models\LessonHour;
use App\Models\Classroom;
use App\Models\AcademicYear;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Collection;

class BulkScheduleService
{
    protected ScheduleValidationService $validationService;

    public function __construct(ScheduleValidationService $validationService)
    {
        $this->validationService = $validationService;
    }

    /**
     * Create multiple schedules at once
     */
    public function createBulkSchedules(array $schedules): array
    {
        $results = [
            'created' => [],
            'errors' => [],
            'conflicts' => []
        ];

        DB::beginTransaction();

        try {
            foreach ($schedules as $index => $scheduleData) {
                $result = $this->createSingleSchedule($scheduleData, $index);
                
                if ($result['success']) {
                    $results['created'][] = $result['schedule'];
                } else {
                    $results['errors'][$index] = $result['error'];
                    if (isset($result['conflicts'])) {
                        $results['conflicts'][$index] = $result['conflicts'];
                    }
                }
            }

            if (empty($results['created']) && !empty($results['errors'])) {
                DB::rollBack();
                return [
                    'success' => false,
                    'message' => 'Tidak ada jadwal yang berhasil dibuat',
                    'results' => $results
                ];
            }

            DB::commit();

            return [
                'success' => true,
                'message' => count($results['created']) . ' jadwal berhasil dibuat',
                'results' => $results
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Create a single schedule with validation
     */
    private function createSingleSchedule(array $scheduleData, int $index): array
    {
        try {
            // Validate required fields
            $requiredFields = ['teacher_assignment_id', 'lesson_hour_id', 'day_of_week'];
            foreach ($requiredFields as $field) {
                if (!isset($scheduleData[$field])) {
                    return [
                        'success' => false,
                        'error' => "Field {$field} is required"
                    ];
                }
            }

            // Check for conflicts
            $conflicts = $this->validationService->validateSchedule(
                $scheduleData['teacher_assignment_id'],
                $scheduleData['lesson_hour_id'],
                $scheduleData['day_of_week']
            );

            if (!empty($conflicts)) {
                $errorMessages = array_map(fn($conflict) => $conflict['message'], $conflicts);
                return [
                    'success' => false,
                    'error' => implode(', ', $errorMessages),
                    'conflicts' => $conflicts
                ];
            }

            // Create the schedule
            $schedule = ClassSchedule::create([
                'teacher_assignment_id' => $scheduleData['teacher_assignment_id'],
                'lesson_hour_id' => $scheduleData['lesson_hour_id'],
                'day_of_week' => $scheduleData['day_of_week'],
                'status' => $scheduleData['status'] ?? 'active',
                'notes' => $scheduleData['notes'] ?? null,
                'is_substitution' => $scheduleData['is_substitution'] ?? false,
                'original_schedule_id' => $scheduleData['original_schedule_id'] ?? null,
                'substitution_date' => $scheduleData['substitution_date'] ?? null,
            ]);

            return [
                'success' => true,
                'schedule' => $schedule->load([
                    'teacherAssignment.teacher.user',
                    'teacherAssignment.subject',
                    'teacherAssignment.classroom',
                    'lessonHour'
                ])
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Copy schedules from one day to another
     */
    public function copySchedulesToDay(
        int $classroomId,
        int $academicYearId,
        string $sourceDay,
        string $targetDay,
        bool $overwrite = false
    ): array {
        if ($sourceDay === $targetDay) {
            throw new \InvalidArgumentException('Source and target days cannot be the same');
        }

        // Get source schedules
        $sourceSchedules = ClassSchedule::with(['teacherAssignment', 'lessonHour'])
            ->whereHas('teacherAssignment', function ($query) use ($classroomId, $academicYearId) {
                $query->where('classroom_id', $classroomId)
                      ->where('academic_year_id', $academicYearId);
            })
            ->where('day_of_week', $sourceDay)
            ->where('status', 'active')
            ->get();

        if ($sourceSchedules->isEmpty()) {
            return [
                'success' => false,
                'message' => 'Tidak ada jadwal di hari sumber',
                'copied' => []
            ];
        }

        DB::beginTransaction();

        try {
            $copiedSchedules = [];

            // If overwrite is true, delete existing schedules on target day
            if ($overwrite) {
                ClassSchedule::whereHas('teacherAssignment', function ($query) use ($classroomId, $academicYearId) {
                    $query->where('classroom_id', $classroomId)
                          ->where('academic_year_id', $academicYearId);
                })
                ->where('day_of_week', $targetDay)
                ->delete();
            }

            foreach ($sourceSchedules as $schedule) {
                // Check if schedule already exists on target day
                $existingSchedule = ClassSchedule::where('teacher_assignment_id', $schedule->teacher_assignment_id)
                    ->where('lesson_hour_id', $schedule->lesson_hour_id)
                    ->where('day_of_week', $targetDay)
                    ->first();

                if (!$existingSchedule) {
                    $newSchedule = ClassSchedule::create([
                        'teacher_assignment_id' => $schedule->teacher_assignment_id,
                        'lesson_hour_id' => $schedule->lesson_hour_id,
                        'day_of_week' => $targetDay,
                        'status' => $schedule->status,
                        'notes' => $schedule->notes,
                        'is_substitution' => false, // Copied schedules are not substitutions
                    ]);

                    $copiedSchedules[] = $newSchedule->load([
                        'teacherAssignment.teacher.user',
                        'teacherAssignment.subject',
                        'lessonHour'
                    ]);
                }
            }

            DB::commit();

            return [
                'success' => true,
                'message' => count($copiedSchedules) . ' jadwal berhasil disalin',
                'copied' => $copiedSchedules
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Generate automatic schedule for a classroom
     */
    public function generateAutoSchedule(
        int $classroomId,
        int $academicYearId,
        array $preferences = []
    ): array {
        // Get teacher assignments for the classroom
        $teacherAssignments = TeacherAssignment::with(['teacher.user', 'subject'])
            ->where('classroom_id', $classroomId)
            ->where('academic_year_id', $academicYearId)
            ->get();

        if ($teacherAssignments->isEmpty()) {
            return [
                'success' => false,
                'message' => 'Tidak ada penugasan guru untuk kelas ini',
                'schedules' => []
            ];
        }

        // Get lesson hours for the classroom
        $lessonHours = LessonHour::where('classroom_id', $classroomId)
            ->orderBy('sequence')
            ->get();

        if ($lessonHours->isEmpty()) {
            return [
                'success' => false,
                'message' => 'Tidak ada jam pelajaran untuk kelas ini',
                'schedules' => []
            ];
        }

        $days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'];
        $generatedSchedules = [];

        DB::beginTransaction();

        try {
            foreach ($days as $day) {
                $daySchedules = $this->generateScheduleForDay(
                    $teacherAssignments,
                    $lessonHours,
                    $day,
                    $preferences
                );
                $generatedSchedules[$day] = $daySchedules;
            }

            DB::commit();

            $totalSchedules = array_sum(array_map('count', $generatedSchedules));

            return [
                'success' => true,
                'message' => "{$totalSchedules} jadwal berhasil dibuat secara otomatis",
                'schedules' => $generatedSchedules
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Generate schedule for a specific day
     */
    private function generateScheduleForDay(
        Collection $teacherAssignments,
        Collection $lessonHours,
        string $day,
        array $preferences
    ): array {
        $daySchedules = [];
        $shuffledAssignments = $teacherAssignments->shuffle();

        // Limit to available lesson hours (excluding breaks)
        $availableHours = $lessonHours->filter(function ($hour) {
            return !str_contains(strtolower($hour->name), 'istirahat') && 
                   !str_contains(strtolower($hour->name), 'break');
        });

        $assignmentIndex = 0;
        foreach ($availableHours as $hour) {
            if ($assignmentIndex >= $shuffledAssignments->count()) {
                $assignmentIndex = 0; // Reset to beginning if we run out of assignments
            }

            $assignment = $shuffledAssignments[$assignmentIndex];

            // Check for conflicts
            $conflicts = $this->validationService->validateSchedule(
                $assignment->id,
                $hour->id,
                $day
            );

            // Only create if no major conflicts
            $hasErrorConflicts = collect($conflicts)->contains('severity', 'error');
            if (!$hasErrorConflicts) {
                $schedule = ClassSchedule::create([
                    'teacher_assignment_id' => $assignment->id,
                    'lesson_hour_id' => $hour->id,
                    'day_of_week' => $day,
                    'status' => 'active',
                    'notes' => 'Generated automatically'
                ]);

                $daySchedules[] = $schedule->load([
                    'teacherAssignment.teacher.user',
                    'teacherAssignment.subject',
                    'lessonHour'
                ]);
            }

            $assignmentIndex++;
        }

        return $daySchedules;
    }
}
