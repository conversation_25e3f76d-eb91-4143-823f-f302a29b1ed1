<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('class_schedules', function (Blueprint $table) {
            // Add unique constraint to prevent duplicate schedules
            // A teacher assignment can only have one schedule per lesson hour and day
            $table->unique(['teacher_assignment_id', 'lesson_hour_id', 'day_of_week'], 'unique_schedule_slot');
            
            // Add indexes for better query performance
            $table->index(['lesson_hour_id', 'day_of_week'], 'idx_lesson_hour_day');
            $table->index(['day_of_week', 'status'], 'idx_day_status');
            $table->index(['teacher_assignment_id', 'status'], 'idx_assignment_status');
            $table->index('status', 'idx_status');
            
            // Add additional fields for better scheduling management
            $table->text('notes')->nullable()->after('status');
            $table->boolean('is_substitution')->default(false)->after('notes');
            $table->foreignId('original_schedule_id')->nullable()->after('is_substitution')->constrained('class_schedules')->onDelete('set null');
            $table->timestamp('substitution_date')->nullable()->after('original_schedule_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('class_schedules', function (Blueprint $table) {
            // Drop unique constraint
            $table->dropUnique('unique_schedule_slot');
            
            // Drop indexes
            $table->dropIndex('idx_lesson_hour_day');
            $table->dropIndex('idx_day_status');
            $table->dropIndex('idx_assignment_status');
            $table->dropIndex('idx_status');
            
            // Drop additional fields
            $table->dropForeign(['original_schedule_id']);
            $table->dropColumn(['notes', 'is_substitution', 'original_schedule_id', 'substitution_date']);
        });
    }
};
