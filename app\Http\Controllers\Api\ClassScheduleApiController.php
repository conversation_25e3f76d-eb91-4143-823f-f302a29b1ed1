<?php

namespace App\Http\Controllers\Api;

use App\Models\Classroom;
use App\Models\LessonHour;
use App\Models\AcademicYear;
use App\Models\ClassSchedule;
use App\Models\TeacherAssignment;
use App\Models\Teacher;
use App\Models\Subject;
use App\Models\ScheduleTemplate;
use App\Services\ScheduleValidationService;
use App\Services\BulkScheduleService;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use App\Http\Requests\ClassScheduleRequests\ClassScheduleStoreRequest;
use App\Http\Requests\ClassScheduleRequests\ClassScheduleUpdateRequest;

class ClassScheduleApiController extends Controller
{
    protected ScheduleValidationService $validationService;
    protected BulkScheduleService $bulkService;

    public function __construct(
        ScheduleValidationService $validationService,
        BulkScheduleService $bulkService
    ) {
        $this->validationService = $validationService;
        $this->bulkService = $bulkService;
    }
    /**
     * Get class management data for Vue component
     */
    public function getClassManagementData(Request $request): JsonResponse
    {
        try {
            $classroomId = $request->input('classroom_id');
            $academicYearId = $request->input('academic_year_id');

            if (!$classroomId || !$academicYearId) {
                return response()->json([
                    'success' => false,
                    'message' => 'Classroom ID dan Academic Year ID diperlukan'
                ], 400);
            }

            // Get classroom with relationships
            $classroom = Classroom::with([
                'teacher.user',
                'program',
                'shift',
                'students'
            ])->findOrFail($classroomId);

            // Get academic year
            $academicYear = AcademicYear::findOrFail($academicYearId);

            // Get teacher assignments for this classroom and academic year
            $teacherAssignments = TeacherAssignment::with([
                'teacher.user',
                'subject',
                'classroom',
                'academicYear'
            ])->where('classroom_id', $classroomId)
                ->where('academic_year_id', $academicYearId)
                ->get();

            // Get class schedules for this classroom and academic year
            $schedules = ClassSchedule::with([
                'teacherAssignment.teacher.user',
                'teacherAssignment.subject',
                'teacherAssignment.classroom',
                'teacherAssignment.academicYear',
                'lessonHour'
            ])->whereHas('teacherAssignment', function ($query) use ($classroomId, $academicYearId) {
                $query->where('classroom_id', $classroomId)
                    ->where('academic_year_id', $academicYearId);
            })->get();

            // Get lesson hours for this classroom if null get global

            $lessonHours = LessonHour::where('classroom_id', $classroomId)
                ->orderBy('sequence')
                ->get()
                ->whenEmpty(function () {
                    return LessonHour::whereNull('classroom_id')
                        ->orderBy('sequence')
                        ->get();
                });


            return response()->json([
                'success' => true,
                'data' => [
                    'classroom' => $classroom,
                    'academicYear' => $academicYear,
                    'teacherAssignments' => $teacherAssignments,
                    'schedules' => $schedules,
                    'lessonHours' => $lessonHours,
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('Error getting class management data: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat memuat data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get teachers for AJAX requests
     */
    public function getTeachers(): JsonResponse
    {
        try {
            $teachers = Teacher::with('user:id,name,email')
                ->whereHas('user', function ($query) {
                    $query->where('status', 'active');
                })
                ->get();

            return response()->json([
                'success' => true,
                'data' => $teachers
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat memuat data guru'
            ], 500);
        }
    }

    /**
     * Get subjects for AJAX requests
     */
    public function getSubjects(): JsonResponse
    {
        try {
            $subjects = Subject::select('id', 'name', 'program_id')
                ->orderBy('name')
                ->get();

            return response()->json([
                'success' => true,
                'data' => $subjects
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat memuat data mata pelajaran'
            ], 500);
        }
    }

    /**
     * Get lesson hours for classroom
     */
    public function getLessonHours(Request $request): JsonResponse
    {
        try {
            $classroomId = $request->input('classroom_id');

            if (!$classroomId) {
                return response()->json([
                    'success' => false,
                    'message' => 'Classroom ID diperlukan'
                ], 400);
            }

        $lessonHours = LessonHour::where('classroom_id', $classroomId)
            ->orderBy('sequence')
            ->get()
            ->whenEmpty(function () {
                return LessonHour::whereNull('classroom_id')
                    ->orderBy('sequence')
                    ->get();
            });


            return response()->json([
                'success' => true,
                'data' => $lessonHours
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat memuat data jam pelajaran'
            ], 500);
        }
    }

    /**
     * Get teacher assignments for classroom and academic year
     */
    public function getTeacherAssignments(Request $request): JsonResponse
    {
        try {
            $classroomId = $request->input('classroom_id');
            $academicYearId = $request->input('academic_year_id');

            if (!$classroomId || !$academicYearId) {
                return response()->json([
                    'success' => false,
                    'message' => 'Classroom ID dan Academic Year ID diperlukan'
                ], 400);
            }

            $teacherAssignments = TeacherAssignment::with([
                'teacher.user',
                'subject'
            ])->where('classroom_id', $classroomId)
                ->where('academic_year_id', $academicYearId)
                ->get();

            return response()->json([
                'success' => true,
                'data' => $teacherAssignments
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat memuat data penugasan guru'
            ], 500);
        }
    }

    /**
     * Store a new class schedule
     */
    public function store(ClassScheduleStoreRequest $request): JsonResponse
    {
        try {
            $classSchedule = ClassSchedule::create($request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Jadwal kelas berhasil disimpan',
                'data' => $classSchedule->load(['teacherAssignment.teacher.user', 'teacherAssignment.subject', 'teacherAssignment.classroom', 'teacherAssignment.academicYear', 'lessonHour'])
            ], 201);

        } catch (\Exception $e) {
            \Log::error('Error creating class schedule: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menyimpan jadwal kelas'
            ], 500);
        }
    }

    /**
     * Update a class schedule
     */
    public function update(ClassScheduleUpdateRequest $request, ClassSchedule $classSchedule): JsonResponse
    {
        try {
            $updated = $classSchedule->update($request->validated());

            return response()->json([
                'success' => true,
                'message' => $updated ? 'Jadwal kelas berhasil diperbarui' : 'Tidak ada perubahan data',
                'data' => $classSchedule->load(['teacherAssignment.teacher.user', 'teacherAssignment.subject', 'teacherAssignment.classroom', 'teacherAssignment.academicYear', 'lessonHour'])
            ]);

        } catch (\Exception $e) {
            \Log::error('Error updating class schedule: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat memperbarui jadwal kelas'
            ], 500);
        }
    }

    /**
     * Delete a class schedule
     */
    public function destroy(ClassSchedule $classSchedule): JsonResponse
    {
        try {
            $classSchedule->delete();

            return response()->json([
                'success' => true,
                'message' => 'Jadwal kelas berhasil dihapus'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menghapus jadwal kelas: ' . $e->getMessage()
            ], 422);
        }
    }

    /**
     * Get class schedule data for editing
     */
    public function getEditData(ClassSchedule $classSchedule): JsonResponse
    {
        try {
            $classSchedule->load([
                'teacherAssignment.teacher.user',
                'teacherAssignment.subject',
                'teacherAssignment.classroom',
                'teacherAssignment.academicYear',
                'lessonHour'
            ]);

            return response()->json([
                'success' => true,
                'data' => $classSchedule
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat memuat data jadwal'
            ], 500);
        }
    }

    /**
     * Check for schedule conflicts
     */
    public function checkConflicts(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'teacher_assignment_id' => 'required|exists:teacher_assignments,id',
                'lesson_hour_id' => 'required|exists:lesson_hours,id',
                'day_of_week' => 'required|in:monday,tuesday,wednesday,thursday,friday,saturday,sunday',
                'exclude_schedule_id' => 'nullable|exists:class_schedules,id'
            ]);

            $conflicts = $this->validationService->validateSchedule(
                $request->teacher_assignment_id,
                $request->lesson_hour_id,
                $request->day_of_week,
                $request->exclude_schedule_id
            );

            return response()->json([
                'success' => true,
                'has_conflicts' => count($conflicts) > 0,
                'conflicts' => $conflicts
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Data tidak valid',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error('Error checking schedule conflicts: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat memeriksa konflik jadwal'
            ], 500);
        }
    }

    /**
     * Get schedule recommendations for a teacher assignment
     */
    public function getRecommendations(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'teacher_assignment_id' => 'required|exists:teacher_assignments,id',
                'day_of_week' => 'required|in:monday,tuesday,wednesday,thursday,friday,saturday,sunday',
                'exclude_schedule_id' => 'nullable|exists:class_schedules,id'
            ]);

            $recommendations = $this->validationService->getScheduleRecommendations(
                $request->teacher_assignment_id,
                $request->day_of_week,
                $request->exclude_schedule_id
            );

            return response()->json([
                'success' => true,
                'recommendations' => $recommendations
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Data tidak valid',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error('Error getting schedule recommendations: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mendapatkan rekomendasi jadwal'
            ], 500);
        }
    }

    /**
     * Bulk create schedules
     */
    public function bulkStore(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'schedules' => 'required|array|min:1',
                'schedules.*.teacher_assignment_id' => 'required|exists:teacher_assignments,id',
                'schedules.*.lesson_hour_id' => 'required|exists:lesson_hours,id',
                'schedules.*.day_of_week' => 'required|in:monday,tuesday,wednesday,thursday,friday,saturday,sunday',
                'schedules.*.notes' => 'nullable|string|max:1000',
                'schedules.*.status' => 'nullable|in:active,inactive',
                'schedules.*.is_substitution' => 'nullable|boolean',
                'schedules.*.original_schedule_id' => 'nullable|exists:class_schedules,id',
                'schedules.*.substitution_date' => 'nullable|date'
            ]);

            $result = $this->bulkService->createBulkSchedules($request->schedules);

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => $result['message'],
                    'data' => $result['results']['created'],
                    'errors' => $result['results']['errors'],
                    'conflicts' => $result['results']['conflicts']
                ], 201);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $result['message'],
                    'errors' => $result['results']['errors'],
                    'conflicts' => $result['results']['conflicts']
                ], 422);
            }

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Data tidak valid',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error('Error bulk creating schedules: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat membuat jadwal'
            ], 500);
        }
    }

    /**
     * Copy schedules from one day to another
     */
    public function copySchedules(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'classroom_id' => 'required|exists:classrooms,id',
                'academic_year_id' => 'required|exists:academic_years,id',
                'source_day' => 'required|in:monday,tuesday,wednesday,thursday,friday,saturday,sunday',
                'target_day' => 'required|in:monday,tuesday,wednesday,thursday,friday,saturday,sunday',
                'overwrite' => 'boolean'
            ]);

            $result = $this->bulkService->copySchedulesToDay(
                $request->classroom_id,
                $request->academic_year_id,
                $request->source_day,
                $request->target_day,
                $request->overwrite ?? false
            );

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => $result['message'],
                    'data' => $result['copied']
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $result['message']
                ], 404);
            }

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Data tidak valid',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error('Error copying schedules: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menyalin jadwal'
            ], 500);
        }
    }

    /**
     * Get available schedule templates
     */
    public function getTemplates(Request $request): JsonResponse
    {
        try {
            $query = ScheduleTemplate::with(['program', 'shift', 'creator'])
                ->active()
                ->orderBy('name');

            if ($request->has('program_id')) {
                $query->byProgram($request->program_id);
            }

            if ($request->has('shift_id')) {
                $query->byShift($request->shift_id);
            }

            if ($request->has('template_type')) {
                $query->byType($request->template_type);
            }

            $templates = $query->get();

            return response()->json([
                'success' => true,
                'data' => $templates
            ]);

        } catch (\Exception $e) {
            Log::error('Error getting schedule templates: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat memuat template jadwal'
            ], 500);
        }
    }

    /**
     * Apply template to classroom
     */
    public function applyTemplate(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'template_id' => 'required|exists:schedule_templates,id',
                'classroom_id' => 'required|exists:classrooms,id',
                'academic_year_id' => 'required|exists:academic_years,id',
                'overwrite' => 'boolean'
            ]);

            $template = ScheduleTemplate::findOrFail($request->template_id);

            DB::beginTransaction();

            // If overwrite is true, delete existing schedules
            if ($request->overwrite) {
                ClassSchedule::whereHas('teacherAssignment', function ($query) use ($request) {
                    $query->where('classroom_id', $request->classroom_id)
                          ->where('academic_year_id', $request->academic_year_id);
                })->delete();
            }

            // Apply template
            $results = $template->applyToClassroom($request->classroom_id, $request->academic_year_id);

            $totalCreated = 0;
            foreach ($results as $dayResults) {
                $totalCreated += count($dayResults);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => "Template berhasil diterapkan. {$totalCreated} jadwal dibuat.",
                'data' => $results
            ]);

        } catch (ValidationException $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Data tidak valid',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error applying schedule template: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menerapkan template'
            ], 500);
        }
    }

    /**
     * Create template from existing classroom schedule
     */
    public function createTemplate(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'name' => 'required|string|max:255',
                'description' => 'nullable|string|max:1000',
                'classroom_id' => 'required|exists:classrooms,id',
                'academic_year_id' => 'required|exists:academic_years,id'
            ]);

            $template = ScheduleTemplate::createFromClassroom(
                $request->classroom_id,
                $request->academic_year_id,
                $request->name,
                $request->description,
                auth()->id()
            );

            return response()->json([
                'success' => true,
                'message' => 'Template jadwal berhasil dibuat',
                'data' => $template->load(['program', 'shift', 'creator'])
            ], 201);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Data tidak valid',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error('Error creating schedule template: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat membuat template'
            ], 500);
        }
    }

    /**
     * Auto-generate schedule for a classroom
     */
    public function autoGenerate(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'classroom_id' => 'required|exists:classrooms,id',
                'academic_year_id' => 'required|exists:academic_years,id',
                'overwrite' => 'boolean',
                'preferences' => 'array'
            ]);

            // If overwrite is true, delete existing schedules
            if ($request->overwrite) {
                ClassSchedule::whereHas('teacherAssignment', function ($query) use ($request) {
                    $query->where('classroom_id', $request->classroom_id)
                          ->where('academic_year_id', $request->academic_year_id);
                })->delete();
            }

            $result = $this->bulkService->generateAutoSchedule(
                $request->classroom_id,
                $request->academic_year_id,
                $request->preferences ?? []
            );

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => $result['message'],
                    'data' => $result['schedules']
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $result['message']
                ], 422);
            }

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Data tidak valid',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error('Error auto-generating schedule: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat membuat jadwal otomatis'
            ], 500);
        }
    }
}
