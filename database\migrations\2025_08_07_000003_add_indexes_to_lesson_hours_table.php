<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('lesson_hours', function (Blueprint $table) {
            // Add indexes for better query performance
            $table->index(['classroom_id', 'sequence'], 'idx_classroom_sequence');
            $table->index(['shift_id', 'sequence'], 'idx_shift_sequence');
            $table->index(['classroom_id', 'shift_id'], 'idx_classroom_shift');
            $table->index('sequence', 'idx_sequence');
            $table->index(['start_time', 'end_time'], 'idx_time_range');
            
            // Add unique constraint for sequence per classroom
            $table->unique(['classroom_id', 'sequence'], 'unique_classroom_sequence');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('lesson_hours', function (Blueprint $table) {
            // Drop indexes
            $table->dropIndex('idx_classroom_sequence');
            $table->dropIndex('idx_shift_sequence');
            $table->dropIndex('idx_classroom_shift');
            $table->dropIndex('idx_sequence');
            $table->dropIndex('idx_time_range');
            
            // Drop unique constraint
            $table->dropUnique('unique_classroom_sequence');
        });
    }
};
