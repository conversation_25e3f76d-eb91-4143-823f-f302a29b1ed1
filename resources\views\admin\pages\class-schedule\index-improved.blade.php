@extends('admin.layouts.app')

@section('title', 'Penjadwalan Kelas')

@section('content')
    @include('admin.components.page-title', [
        'title' => 'Penjadwalan Kelas',
        'breadcrumb' => 'Manajemen Sekolah',
        'breadcrumb_items' => [
            [
                'text' => 'Kelas',
                'url' => route('admin.classrooms.index'),
            ],
            [
                'text' => 'Penjadwalan',
                'url' => null,
            ],
        ],
    ])

    <div id="schedule-app">
        <!-- Filter Section -->
        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header border-bottom-dashed">
                        <div class="d-flex flex-column flex-sm-row justify-content-between align-items-center gap-3">
                            <div>
                                <h5 class="card-title mb-0 d-flex align-items-center">
                                    <i class="ri-calendar-schedule-line me-2"></i>Pen<PERSON><PERSON><PERSON><PERSON>
                                    <span class="badge bg-primary-subtle text-primary ms-2" v-if="classData.schedules">
                                        @{{ classData.schedules.length }} Jadwal
                                    </span>
                                </h5>
                            </div>
                            <div class="d-flex gap-2">
                                <button class="btn btn-ghost-secondary btn-sm" @click="refreshData" :disabled="loading">
                                    <i class="ri-refresh-line me-1"></i>Refresh
                                </button>
                                <button class="btn btn-success btn-sm" @click="showBulkModal" v-if="selectedClassroom && selectedAcademicYear">
                                    <i class="ri-add-box-line me-1"></i>Bulk Add
                                </button>
                                <button class="btn btn-info btn-sm" @click="showCopyModal" v-if="selectedClassroom && selectedAcademicYear">
                                    <i class="ri-file-copy-line me-1"></i>Copy Day
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <label class="form-label">Kelas <span class="text-danger">*</span></label>
                                <select class="form-select" v-model="selectedClassroom" @change="onFilterChange" :disabled="loading">
                                    <option value="">Pilih Kelas</option>
                                    <option v-for="classroom in classrooms" :key="classroom.id" :value="classroom.id">
                                        @{{ classroom.name }}
                                    </option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Tahun Akademik <span class="text-danger">*</span></label>
                                <select class="form-select" v-model="selectedAcademicYear" @change="onFilterChange" :disabled="loading">
                                    <option value="">Pilih Tahun Akademik</option>
                                    <option v-for="year in academicYears" :key="year.id" :value="year.id">
                                        @{{ year.formatted_name }}
                                    </option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label d-block" style="visibility: hidden;">Action</label>
                                <button class="btn btn-primary w-100" @click="loadData" :disabled="!selectedClassroom || !selectedAcademicYear || loading">
                                    <i class="ri-search-line me-1"></i>
                                    <span v-if="loading">Memuat...</span>
                                    <span v-else>Tampilkan Jadwal</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Schedule Grid -->
        <div class="row mt-4" v-if="classData.classroom">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header border-bottom-dashed">
                        <div class="d-flex flex-column flex-sm-row justify-content-between align-items-center gap-3">
                            <div>
                                <h5 class="card-title mb-0">@{{ classData.classroom.name }} - @{{ classData.academicYear.formatted_name }}</h5>
                                <p class="text-muted mb-0">
                                    <small>
                                        Wali Kelas: @{{ classData.classroom.teacher?.user?.name || 'Belum ditugaskan' }} |
                                        Siswa: @{{ classData.classroom.students_count }} |
                                        Program: @{{ classData.classroom.program?.name }}
                                    </small>
                                </p>
                            </div>
                            <div class="schedule-legend d-flex gap-2">
                                <span class="badge bg-primary-subtle text-primary">Tersedia</span>
                                <span class="badge bg-success-subtle text-success">Terjadwal</span>
                                <span class="badge bg-warning-subtle text-warning">Konflik</span>
                                <span class="badge bg-danger-subtle text-danger">Error</span>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="schedule-container">
                            <!-- Loading Overlay -->
                            <div v-if="loading" class="schedule-loading">
                                <div class="text-center">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <p class="mt-2">Memuat jadwal...</p>
                                </div>
                            </div>

                            <!-- Schedule Table -->
                            <div v-else-if="classData.lessonHours && classData.lessonHours.length > 0" class="table-responsive">
                                <table class="table table-bordered schedule-table mb-0">
                                    <thead class="table-dark">
                                        <tr>
                                            <th class="time-column">Jam Pelajaran</th>
                                            <th v-for="(dayName, dayKey) in days" :key="dayKey" class="day-column">
                                                @{{ dayName }}
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr v-for="hour in classData.lessonHours" :key="hour.id" class="schedule-row">
                                            <td class="time-cell">
                                                <div class="time-info">
                                                    <div class="fw-bold">@{{ hour.name }}</div>
                                                    <small class="text-muted">
                                                        @{{ hour.formatted_start_time }} - @{{ hour.formatted_end_time }}
                                                    </small>
                                                </div>
                                            </td>
                                            <td v-for="(dayName, dayKey) in days" :key="`${hour.id}-${dayKey}`"
                                                class="schedule-cell"
                                                :class="getCellClass(hour.id, dayKey)"
                                                @drop="onDrop($event, hour.id, dayKey)"
                                                @dragover.prevent
                                                @dragenter.prevent
                                                @dragleave="onDragLeave($event)"
                                                @click="onCellClick(hour.id, dayKey)">

                                                <!-- Existing Schedule -->
                                                <div v-if="getSchedule(hour.id, dayKey)"
                                                     class="schedule-item"
                                                     :class="getScheduleClass(hour.id, dayKey)"
                                                     :draggable="true"
                                                     @dragstart="onDragStart($event, getSchedule(hour.id, dayKey))"
                                                     @dragend="onDragEnd($event)">

                                                    <div class="schedule-content">
                                                        <div class="subject-name">
                                                            @{{ getAssignment(getSchedule(hour.id, dayKey).teacher_assignment_id)?.subject?.name || 'N/A' }}
                                                        </div>
                                                        <div class="teacher-name">
                                                            @{{ getAssignment(getSchedule(hour.id, dayKey).teacher_assignment_id)?.teacher?.user?.name || 'N/A' }}
                                                        </div>
                                                        <div v-if="getSchedule(hour.id, dayKey).notes" class="schedule-notes">
                                                            <small>@{{ getSchedule(hour.id, dayKey).notes }}</small>
                                                        </div>
                                                    </div>

                                                    <!-- Schedule Actions -->
                                                    <div class="schedule-actions">
                                                        <button class="btn btn-sm btn-primary" @click.stop="editSchedule(getSchedule(hour.id, dayKey))">
                                                            <i class="ri-pencil-line"></i>
                                                        </button>
                                                        <button class="btn btn-sm btn-danger" @click.stop="confirmDeleteSchedule(getSchedule(hour.id, dayKey))">
                                                            <i class="ri-delete-bin-line"></i>
                                                        </button>
                                                    </div>
                                                </div>

                                                <!-- Empty Cell -->
                                                <div v-else class="empty-cell">
                                                    <button class="btn btn-sm btn-outline-primary add-btn" @click="addSchedule(hour.id, dayKey)">
                                                        <i class="ri-add-line"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <!-- No Lesson Hours -->
                            <div v-else class="text-center py-5">
                                <div class="mb-3">
                                    <i class="ri-calendar-line display-4 text-muted"></i>
                                </div>
                                <h6>Belum ada jam pelajaran</h6>
                                <p class="text-muted">Tambahkan jam pelajaran terlebih dahulu untuk membuat jadwal.</p>
                                <a :href="'/admin/lesson-hours/create'" class="btn btn-primary">
                                    <i class="ri-add-line me-1"></i>Tambah Jam Pelajaran
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- No Data Selected -->
        <div v-else class="row">
            <div class="col-lg-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center py-5">
                        <div class="mb-3">
                            <i class="ri-calendar-check-line display-4 text-muted"></i>
                        </div>
                        <h6>Pilih Kelas dan Tahun Akademik</h6>
                        <p class="text-muted">Silakan pilih kelas dan tahun akademik untuk mengelola jadwal pelajaran</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Teacher Assignments Panel -->
        <div class="row mt-4" v-if="classData.classroom">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header border-bottom-dashed">
                        <div class="d-flex flex-column flex-sm-row justify-content-between align-items-center gap-3">
                            <div>
                                <h5 class="card-title mb-0 d-flex align-items-center">
                                    <i class="ri-user-star-line me-2"></i>Penugasan Guru
                                    <span class="badge bg-primary-subtle text-primary ms-2">@{{ classData.teacherAssignments?.length || 0 }}</span>
                                </h5>
                            </div>
                            <div>
                                <button class="btn btn-primary btn-sm" @click="showTeacherModal">
                                    <i class="ri-add-line me-1"></i>Tambah Penugasan
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row g-3" v-if="classData.teacherAssignments && classData.teacherAssignments.length > 0">
                            <div v-for="assignment in classData.teacherAssignments" :key="assignment.id" class="col-md-6 col-lg-4">
                                <div class="assignment-card p-3 border rounded"
                                     :style="{ borderLeft: `4px solid ${getSubjectColor(assignment.subject?.id)}` }"
                                     draggable="true"
                                     @dragstart="onAssignmentDragStart($event, assignment)">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">@{{ assignment.subject?.name || 'N/A' }}</h6>
                                            <p class="mb-1 text-muted">@{{ assignment.teacher?.user?.name || 'N/A' }}</p>
                                            <small v-if="assignment.is_homeroom_teacher" class="badge bg-success">Wali Kelas</small>
                                        </div>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-ghost-secondary" data-bs-toggle="dropdown">
                                                <i class="ri-more-2-line"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="#" @click="editAssignment(assignment)">
                                                        <i class="ri-pencil-line me-2"></i>Edit
                                                    </a></li>
                                                <li><a class="dropdown-item text-danger" href="#" @click="confirmDeleteAssignment(assignment)">
                                                        <i class="ri-delete-bin-line me-2"></i>Hapus
                                                    </a></li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-else class="text-center py-4">
                            <i class="ri-user-star-line display-6 text-muted"></i>
                            <p class="text-muted mt-2">Belum ada penugasan guru</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('styles')
    <style>
        /* Schedule Container */
        .schedule-container {
            position: relative;
            min-height: 400px;
        }

        .schedule-loading {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }

        /* Schedule Table */
        .schedule-table {
            font-size: 0.875rem;
        }

        .time-column {
            width: 150px;
            min-width: 150px;
        }

        .day-column {
            width: calc((100% - 150px) / 7);
            min-width: 120px;
            text-align: center;
        }

        .time-cell {
            background: #f8f9fa;
            vertical-align: middle;
            padding: 12px;
        }

        .time-info {
            text-align: center;
        }

        /* Schedule Cells */
        .schedule-cell {
            position: relative;
            height: 80px;
            min-height: 80px;
            padding: 0;
            vertical-align: top;
            border: 2px solid #dee2e6;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .schedule-cell:hover {
            background-color: rgba(var(--vz-primary-rgb), 0.05);
            border-color: var(--vz-primary);
        }

        .schedule-cell.drag-over {
            background-color: rgba(var(--vz-success-rgb), 0.1);
            border-color: var(--vz-success);
            border-style: dashed;
        }

        .schedule-cell.has-conflict {
            background-color: rgba(var(--vz-danger-rgb), 0.1);
            border-color: var(--vz-danger);
        }

        .schedule-cell.empty {
            background-color: #f8f9fa;
        }

        /* Schedule Items */
        .schedule-item {
            position: relative;
            height: 100%;
            width: 100%;
            padding: 8px;
            background: var(--vz-primary);
            color: white;
            border-radius: 0.375rem;
            cursor: move;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .schedule-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }

        .schedule-item.dragging {
            opacity: 0.5;
            transform: rotate(5deg);
            z-index: 1000;
        }

        .schedule-content {
            flex-grow: 1;
            text-align: center;
        }

        .subject-name {
            font-weight: 600;
            font-size: 0.8rem;
            line-height: 1.2;
            margin-bottom: 2px;
        }

        .teacher-name {
            font-size: 0.7rem;
            opacity: 0.9;
            line-height: 1.1;
        }

        .schedule-notes {
            font-size: 0.65rem;
            opacity: 0.8;
            margin-top: 2px;
        }

        /* Schedule Actions */
        .schedule-actions {
            position: absolute;
            top: 2px;
            right: 2px;
            display: none;
            gap: 2px;
        }

        .schedule-item:hover .schedule-actions {
            display: flex;
        }

        .schedule-actions .btn {
            padding: 2px 4px;
            font-size: 0.7rem;
            line-height: 1;
        }

        /* Empty Cells */
        .empty-cell {
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .add-btn {
            opacity: 0;
            transition: all 0.3s ease;
        }

        .schedule-cell:hover .add-btn {
            opacity: 1;
        }

        /* Assignment Cards */
        .assignment-card {
            transition: all 0.3s ease;
            cursor: grab;
        }

        .assignment-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .assignment-card:active {
            cursor: grabbing;
        }



        /* Legend */
        .schedule-legend .badge {
            font-size: 0.7rem;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .schedule-table {
                font-size: 0.75rem;
            }

            .time-column {
                width: 100px;
                min-width: 100px;
            }

            .schedule-cell {
                height: 60px;
                min-height: 60px;
            }

            .subject-name {
                font-size: 0.7rem;
            }

            .teacher-name {
                font-size: 0.65rem;
            }
        }

        /* Animation */
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .schedule-item {
            animation: slideIn 0.3s ease-out;
        }
    </style>
@endpush

@include('admin.partials.plugins.jquery')
@include('admin.partials.plugins.sweetalert')

@push('scripts')
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>

    <script>
        const {
            createApp
        } = Vue;

        createApp({
                data() {
                    return {
                        classrooms: @json($classrooms),
                        academicYears: @json($academicYears),
                        selectedClassroom: '{{ request('classroom_id') }}',
                        selectedAcademicYear: '{{ request('academic_year_id') }}',
                        classData: {},
                        loading: false,
                        draggedItem: null,
                        draggedType: null, // 'schedule' or 'assignment'
                        conflicts: {},
                        days: {
                            monday: 'Senin',
                            tuesday: 'Selasa',
                            wednesday: 'Rabu',
                            thursday: 'Kamis',
                            friday: 'Jumat',
                            saturday: 'Sabtu',
                            sunday: 'Minggu'
                        },
                        subjectColors: {},
                        colorPalette: [
                            "#3498db", "#2ecc71", "#9b59b6", "#e74c3c", "#f39c12",
                            "#1abc9c", "#d35400", "#34495e", "#16a085", "#27ae60",
                            "#8e44ad", "#f1c40f", "#e67e22", "#c0392b", "#7f8c8d"
                        ]
                    };
                },

                computed: {
                    hasData() {
                        return this.selectedClassroom && this.selectedAcademicYear && this.classData.classroom;
                    }
                },

                mounted() {
                    if (this.selectedClassroom && this.selectedAcademicYear) {
                        this.loadData();
                    }
                },

                methods: {
                    async loadData() {
                        if (!this.selectedClassroom || !this.selectedAcademicYear) return;

                        this.loading = true;
                        try {
                            const response = await fetch(`/api/class-schedules/management-data?classroom_id=${this.selectedClassroom}&academic_year_id=${this.selectedAcademicYear}`, {
                                method: 'GET',
                                headers: {
                                    'Accept': 'application/json',
                                    'Content-Type': 'application/json',
                                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                                    'X-Requested-With': 'XMLHttpRequest'
                                },
                                credentials: 'same-origin'
                            });

                            if (!response.ok) {
                                throw new Error(`HTTP error! status: ${response.status}`);
                            }

                            const data = await response.json();
                            if (data.success) {
                                this.classData = data.data;
                                this.generateSubjectColors();
                            } else {
                                this.showAlert(data.message || 'Gagal memuat data', 'error');
                            }
                        } catch (error) {
                            this.showAlert('Terjadi kesalahan saat memuat data: ' + error.message, 'error');
                            console.error('Error loading data:', error);
                        } finally {
                            this.loading = false;
                        }
                    },

                    onFilterChange() {
                        this.classData = {};
                        this.conflicts = {};
                    },

                    generateSubjectColors() {
                        if (!this.classData.teacherAssignments) return;

                        const subjects = [...new Set(this.classData.teacherAssignments.map(a => a.subject?.id).filter(Boolean))];
                        subjects.forEach((subjectId, index) => {
                            this.subjectColors[subjectId] = this.colorPalette[index % this.colorPalette.length];
                        });
                    },

                    getSubjectColor(subjectId) {
                        return this.subjectColors[subjectId] || '#6c757d';
                    },

                    getSchedule(lessonHourId, dayOfWeek) {
                        return this.classData.schedules?.find(s =>
                            s.lesson_hour_id === lessonHourId && s.day_of_week === dayOfWeek
                        ) || null;
                    },

                    getAssignment(assignmentId) {
                        return this.classData.teacherAssignments?.find(a => a.id === assignmentId) || null;
                    },

                    getCellClass(lessonHourId, dayOfWeek) {
                        const schedule = this.getSchedule(lessonHourId, dayOfWeek);
                        const conflictKey = `${lessonHourId}-${dayOfWeek}`;

                        return {
                            'empty': !schedule,
                            'has-schedule': !!schedule,
                            'has-conflict': this.conflicts[conflictKey],
                            'drag-over': false
                        };
                    },

                    getScheduleClass(lessonHourId, dayOfWeek) {
                        const schedule = this.getSchedule(lessonHourId, dayOfWeek);
                        if (!schedule) return '';

                        const assignment = this.getAssignment(schedule.teacher_assignment_id);
                        const color = this.getSubjectColor(assignment?.subject?.id);

                        return {
                            'dragging': this.draggedItem && this.draggedItem.id === schedule.id
                        };
                    },

                    // Drag and Drop Events
                    onDragStart(event, item) {
                        this.draggedItem = item;
                        this.draggedType = 'schedule';
                        event.dataTransfer.setData('text/plain', item.id);
                        event.target.classList.add('dragging');
                    },

                    onAssignmentDragStart(event, assignment) {
                        this.draggedItem = assignment;
                        this.draggedType = 'assignment';
                        event.dataTransfer.setData('text/plain', assignment.id);
                    },

                    onDragEnd(event) {
                        event.target.classList.remove('dragging');
                        this.draggedItem = null;
                        this.draggedType = null;
                        // Remove drag-over classes
                        document.querySelectorAll('.drag-over').forEach(el => {
                            el.classList.remove('drag-over');
                        });
                    },

                    onDragLeave(event) {
                        event.target.classList.remove('drag-over');
                    },

                    async onDrop(event, lessonHourId, dayOfWeek) {
                        event.preventDefault();
                        event.target.classList.remove('drag-over');

                        if (!this.draggedItem) return;

                        if (this.draggedType === 'schedule') {
                            await this.moveSchedule(lessonHourId, dayOfWeek);
                        } else if (this.draggedType === 'assignment') {
                            await this.createScheduleFromAssignment(lessonHourId, dayOfWeek);
                        }
                    },

                    async moveSchedule(lessonHourId, dayOfWeek) {
                        if (!this.draggedItem || this.draggedType !== 'schedule') return;

                        // Check if moving to same position
                        if (this.draggedItem.lesson_hour_id === lessonHourId && this.draggedItem.day_of_week === dayOfWeek) {
                            this.showAlert('Jadwal tidak dapat dipindahkan ke posisi yang sama', 'warning');
                            return;
                        }

                        // Check for conflicts
                        const conflicts = await this.checkConflicts(this.draggedItem.teacher_assignment_id, lessonHourId, dayOfWeek, this.draggedItem.id);
                        if (conflicts.has_conflicts) {
                            this.showConflictAlert(conflicts.conflicts);
                            return;
                        }

                        this.loading = true;
                        try {
                            const formData = new FormData();
                            formData.append('teacher_assignment_id', this.draggedItem.teacher_assignment_id);
                            formData.append('lesson_hour_id', lessonHourId);
                            formData.append('day_of_week', dayOfWeek);
                            formData.append('classroom_id', this.selectedClassroom);
                            formData.append('academic_year_id', this.selectedAcademicYear);
                            formData.append('_method', 'PUT');

                            const response = await fetch(`/api/class-schedules/${this.draggedItem.id}`, {
                                method: 'POST',
                                headers: {
                                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                                    'Accept': 'application/json'
                                },
                                body: formData
                            });

                            const data = await response.json();
                            if (data.success) {
                                this.showAlert('Jadwal berhasil dipindahkan', 'success');
                                await this.loadData();
                            } else {
                                throw new Error(data.message || 'Gagal memindahkan jadwal');
                            }
                        } catch (error) {
                            this.showAlert(error.message, 'error');
                        } finally {
                            this.loading = false;
                        }
                    },

                    async createScheduleFromAssignment(lessonHourId, dayOfWeek) {
                        if (!this.draggedItem || this.draggedType !== 'assignment') return;

                        // Check if slot is already occupied
                        const existingSchedule = this.getSchedule(lessonHourId, dayOfWeek);
                        if (existingSchedule) {
                            this.showAlert('Slot ini sudah terisi. Pilih slot kosong.', 'warning');
                            return;
                        }

                        // Check for conflicts
                        const conflicts = await this.checkConflicts(this.draggedItem.id, lessonHourId, dayOfWeek);
                        if (conflicts.has_conflicts) {
                            this.showConflictAlert(conflicts.conflicts);
                            return;
                        }

                        this.loading = true;
                        try {
                            const response = await fetch('/api/class-schedules', {
                                method: 'POST',
                                headers: {
                                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                                    'Accept': 'application/json',
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify({
                                    teacher_assignment_id: this.draggedItem.id,
                                    lesson_hour_id: lessonHourId,
                                    day_of_week: dayOfWeek,
                                    status: 'active'
                                })
                            });

                            const data = await response.json();
                            if (data.success) {
                                this.showAlert('Jadwal berhasil dibuat', 'success');
                                await this.loadData();
                            } else {
                                throw new Error(data.message || 'Gagal membuat jadwal');
                            }
                        } catch (error) {
                            this.showAlert(error.message, 'error');
                        } finally {
                            this.loading = false;
                        }
                    },

                    async checkConflicts(teacherAssignmentId, lessonHourId, dayOfWeek, excludeScheduleId = null) {
                        try {
                            const response = await fetch('/api/class-schedules/check-conflicts', {
                                method: 'POST',
                                headers: {
                                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                                    'Accept': 'application/json',
                                    'Content-Type': 'application/json',
                                    'X-Requested-With': 'XMLHttpRequest'
                                },
                                credentials: 'same-origin',
                                body: JSON.stringify({
                                    teacher_assignment_id: teacherAssignmentId,
                                    lesson_hour_id: lessonHourId,
                                    day_of_week: dayOfWeek,
                                    exclude_schedule_id: excludeScheduleId
                                })
                            });

                            if (!response.ok) {
                                throw new Error(`HTTP error! status: ${response.status}`);
                            }

                            const data = await response.json();
                            return data.success ? data : {
                                has_conflicts: false,
                                conflicts: []
                            };
                        } catch (error) {
                            console.error('Error checking conflicts:', error);
                            return {
                                has_conflicts: false,
                                conflicts: []
                            };
                        }
                    },

                    showConflictAlert(conflicts) {
                        let message = 'Konflik jadwal ditemukan:\n\n';
                        conflicts.forEach(conflict => {
                            message += `• ${conflict.message}\n`;
                            if (conflict.details) {
                                message += `  ${JSON.stringify(conflict.details)}\n`;
                            }
                        });

                        Swal.fire({
                            title: 'Konflik Jadwal!',
                            text: message,
                            icon: 'warning',
                            confirmButtonText: 'OK',
                            confirmButtonClass: 'btn btn-primary'
                        });
                    },

                    onCellClick(lessonHourId, dayOfWeek) {
                        const schedule = this.getSchedule(lessonHourId, dayOfWeek);
                        if (schedule) {
                            this.editSchedule(schedule);
                        } else {
                            this.addSchedule(lessonHourId, dayOfWeek);
                        }
                    },

                    addSchedule(lessonHourId, dayOfWeek) {
                        // Implementation for adding schedule modal
                        console.log('Add schedule:', lessonHourId, dayOfWeek);
                    },

                    editSchedule(schedule) {
                        // Implementation for editing schedule modal
                        console.log('Edit schedule:', schedule);
                    },

                    confirmDeleteSchedule(schedule) {
                        Swal.fire({
                            title: 'Konfirmasi Hapus',
                            text: 'Jadwal ini akan dihapus permanen. Lanjutkan?',
                            icon: 'warning',
                            showCancelButton: true,
                            confirmButtonText: 'Ya, Hapus!',
                            cancelButtonText: 'Batal',
                            confirmButtonClass: 'btn btn-danger',
                            cancelButtonClass: 'btn btn-secondary'
                        }).then(result => {
                            if (result.isConfirmed) {
                                this.deleteSchedule(schedule);
                            }
                        });
                    },

                    async deleteSchedule(schedule) {
                        this.loading = true;
                        try {
                            const response = await fetch(`/api/class-schedules/${schedule.id}`, {
                                method: 'DELETE',
                                headers: {
                                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                                    'Accept': 'application/json'
                                }
                            });

                            const data = await response.json();
                            if (data.success) {
                                this.showAlert('Jadwal berhasil dihapus', 'success');
                                await this.loadData();
                            } else {
                                throw new Error(data.message || 'Gagal menghapus jadwal');
                            }
                        } catch (error) {
                            this.showAlert(error.message, 'error');
                        } finally {
                            this.loading = false;
                        }
                    },

                    // Utility methods
                    refreshData() {
                        this.loadData();
                    },

                    showBulkModal() {
                        // Implementation for bulk add modal
                        console.log('Show bulk modal');
                    },

                    showCopyModal() {
                        // Implementation for copy day modal
                        console.log('Show copy modal');
                    },

                    showTeacherModal() {
                        // Implementation for teacher assignment modal
                        console.log('Show teacher modal');
                    },

                    editAssignment(assignment) {
                        // Implementation for editing assignment
                        console.log('Edit assignment:', assignment);
                    },

                    confirmDeleteAssignment(assignment) {
                        Swal.fire({
                            title: 'Konfirmasi Hapus',
                            text: 'Penugasan guru ini akan dihapus permanen. Lanjutkan?',
                            icon: 'warning',
                            showCancelButton: true,
                            confirmButtonText: 'Ya, Hapus!',
                            cancelButtonText: 'Batal',
                            confirmButtonClass: 'btn btn-danger',
                            cancelButtonClass: 'btn btn-secondary'
                        }).then(result => {
                            if (result.isConfirmed) {
                                this.deleteAssignment(assignment);
                            }
                        });
                    },

                    async deleteAssignment(assignment) {
                        this.loading = true;
                        try {
                            const response = await fetch(`/admin/teacher-assignments/${assignment.id}`, {
                                method: 'DELETE',
                                headers: {
                                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                                    'Accept': 'application/json'
                                }
                            });

                            const data = await response.json();
                            if (data.success) {
                                this.showAlert('Penugasan guru berhasil dihapus', 'success');
                                await this.loadData();
                            } else {
                                throw new Error(data.message || 'Gagal menghapus penugasan');
                            }
                        } catch (error) {
                            this.showAlert(error.message, 'error');
                        } finally {
                            this.loading = false;
                        }
                    },

                    showAlert(message, type = 'info') {
                        const icons = {
                            success: 'success',
                            error: 'error',
                            warning: 'warning',
                            info: 'info'
                        };

                        const titles = {
                            success: 'Berhasil!',
                            error: 'Error!',
                            warning: 'Perhatian!',
                            info: 'Informasi'
                        };

                        Swal.fire({
                            title: titles[type] || 'Informasi',
                            text: message,
                            icon: icons[type] || 'info',
                            confirmButtonText: 'OK',
                            confirmButtonClass: 'btn btn-primary'
                        });
                    }
                }
            })
            .mount('#schedule-app');
    </script>
@endpush
