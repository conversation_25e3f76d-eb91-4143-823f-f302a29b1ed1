<?php

namespace Database\Seeders;

use App\Models\Teacher;
use App\Models\Subject;
use App\Models\Classroom;
use App\Models\AcademicYear;
use App\Models\TeacherAssignment;
use Illuminate\Database\Seeder;

class TeacherAssignmentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $teachers = Teacher::all();
        $subjects = Subject::all();
        $classrooms = Classroom::all();
        $academicYears = AcademicYear::all();

        if ($teachers->isEmpty() || $subjects->isEmpty() || $classrooms->isEmpty() || $academicYears->isEmpty()) {
            $this->command->warn('Skipping TeacherAssignmentSeeder: Required data not found.');
            return;
        }

        $assignments = [];

        // Create assignments for each classroom
        foreach ($classrooms as $classroom) {
            $academicYear = $academicYears->first();

            // Assign homeroom teacher
            $homeroomTeacher = $teachers->random();
            $assignments[] = [
                'teacher_id' => $homeroomTeacher->id,
                'subject_id' => $subjects->where('name', 'like', '%Agama%')->first()?->id ?? $subjects->first()->id,
                'classroom_id' => $classroom->id,
                'academic_year_id' => $academicYear->id,
                'is_homeroom_teacher' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ];

            // Assign subject teachers (3-5 subjects per classroom)
            $subjectCount = rand(3, 5);
            $selectedSubjects = $subjects->random($subjectCount);
            $availableTeachers = $teachers->where('id', '!=', $homeroomTeacher->id);

            foreach ($selectedSubjects as $subject) {
                $teacher = $availableTeachers->random();
                $assignments[] = [
                    'teacher_id' => $teacher->id,
                    'subject_id' => $subject->id,
                    'classroom_id' => $classroom->id,
                    'academic_year_id' => $academicYear->id,
                    'is_homeroom_teacher' => false,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }
        }

        // Insert assignments in chunks to avoid memory issues
        foreach (array_chunk($assignments, 100) as $chunk) {
            TeacherAssignment::insert($chunk);
        }

        $this->command->info('Teacher assignments seeded successfully!');
    }
}
