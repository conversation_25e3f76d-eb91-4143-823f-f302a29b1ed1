<?php

namespace App\Models;

use App\Enums\StatusEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ClassSchedule extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'teacher_assignment_id',
        'lesson_hour_id',
        'day_of_week',
        'status',
        'notes',
        'is_substitution',
        'original_schedule_id',
        'substitution_date',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'status' => StatusEnum::class,
        'is_substitution' => 'boolean',
        'substitution_date' => 'datetime',
    ];

    /**
     * Get the classroom through teacher assignment
     */
    public function classroom()
    {
        return $this->hasOneThrough(Classroom::class, TeacherAssignment::class, 'id', 'id', 'teacher_assignment_id', 'classroom_id');
    }

    /**
     * Get the subject through teacher assignment
     */
    public function subject()
    {
        return $this->hasOneThrough(Subject::class, TeacherAssignment::class, 'id', 'id', 'teacher_assignment_id', 'subject_id');
    }

    /**
     * Get the teacher through teacher assignment
     */
    public function teacher()
    {
        return $this->hasOneThrough(Teacher::class, TeacherAssignment::class, 'id', 'id', 'teacher_assignment_id', 'teacher_id');
    }

    /**
     * Get the academic year through teacher assignment
     */
    public function academicYear()
    {
        return $this->hasOneThrough(AcademicYear::class, TeacherAssignment::class, 'id', 'id', 'teacher_assignment_id', 'academic_year_id');
    }

    /**
     * Get the lesson hour that owns the class schedule
     */
    public function lessonHour(): BelongsTo
    {
        return $this->belongsTo(LessonHour::class);
    }

    /**
     * Get the teacher assignment that owns the class schedule
     */
    public function teacherAssignment(): BelongsTo
    {
        return $this->belongsTo(TeacherAssignment::class);
    }

    /**
     * Get the original schedule for substitutions
     */
    public function originalSchedule(): BelongsTo
    {
        return $this->belongsTo(ClassSchedule::class, 'original_schedule_id');
    }

    /**
     * Get the substitution schedules
     */
    public function substitutionSchedules(): HasMany
    {
        return $this->hasMany(ClassSchedule::class, 'original_schedule_id');
    }

    /**
     * Scope a query to only include active class schedules.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to only include regular schedules (not substitutions).
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeRegular($query)
    {
        return $query->where('is_substitution', false);
    }

    /**
     * Scope a query to only include substitution schedules.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSubstitutions($query)
    {
        return $query->where('is_substitution', true);
    }

    /**
     * Scope a query to filter by day of week.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $day
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByDay($query, $day)
    {
        return $query->where('day_of_week', $day);
    }

    /**
     * Scope a query to filter by classroom through teacher assignment.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  int  $classroomId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByClassroom($query, $classroomId)
    {
        return $query->whereHas('teacherAssignment', function ($q) use ($classroomId) {
            $q->where('classroom_id', $classroomId);
        });
    }

    /**
     * Scope a query to filter by academic year through teacher assignment.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  int  $academicYearId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByAcademicYear($query, $academicYearId)
    {
        return $query->whereHas('teacherAssignment', function ($q) use ($academicYearId) {
            $q->where('academic_year_id', $academicYearId);
        });
    }
}
