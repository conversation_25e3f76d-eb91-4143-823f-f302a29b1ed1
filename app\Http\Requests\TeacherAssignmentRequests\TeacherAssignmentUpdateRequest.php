<?php

namespace App\Http\Requests\TeacherAssignmentRequests;

use Illuminate\Foundation\Http\FormRequest;

class TeacherAssignmentUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'teacher_id' => ['required', 'integer', 'exists:teachers,id'],
            'subject_id' => ['nullable', 'integer', 'exists:subjects,id'],
            'classroom_id' => ['required', 'integer', 'exists:classrooms,id'],
            'academic_year_id' => ['required', 'integer', 'exists:academic_years,id'],
            'is_homeroom_teacher' => ['nullable', 'boolean'],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'teacher_id' => 'Guru',
            'subject_id' => '<PERSON>',
            'classroom_id' => 'Kelas',
            'academic_year_id' => 'Tahun Akademik',
            'is_homeroom_teacher' => 'Status Wali Kelas',
        ];
    }
}
