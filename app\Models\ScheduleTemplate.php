<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ScheduleTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'program_id',
        'shift_id',
        'template_type',
        'template_data',
        'is_active',
        'created_by',
    ];

    protected $casts = [
        'template_data' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Get the program that owns the template.
     */
    public function program(): BelongsTo
    {
        return $this->belongsTo(Program::class);
    }

    /**
     * Get the shift that owns the template.
     */
    public function shift(): BelongsTo
    {
        return $this->belongsTo(Shift::class);
    }

    /**
     * Get the user who created the template.
     */
    public function creator(): <PERSON>ong<PERSON>T<PERSON>
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope a query to only include active templates.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to filter by template type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('template_type', $type);
    }

    /**
     * Scope a query to filter by program.
     */
    public function scopeByProgram($query, $programId)
    {
        return $query->where('program_id', $programId);
    }

    /**
     * Scope a query to filter by shift.
     */
    public function scopeByShift($query, $shiftId)
    {
        return $query->where('shift_id', $shiftId);
    }

    /**
     * Get template data for a specific day
     */
    public function getTemplateForDay(string $day): array
    {
        $templateData = $this->template_data;
        
        if ($this->template_type === 'weekly') {
            return $templateData[$day] ?? [];
        }
        
        if ($this->template_type === 'daily') {
            return $templateData;
        }
        
        return $templateData[$day] ?? [];
    }

    /**
     * Apply template to a classroom
     */
    public function applyToClassroom(int $classroomId, int $academicYearId): array
    {
        $results = [];
        $templateData = $this->template_data;

        if ($this->template_type === 'weekly') {
            foreach ($templateData as $day => $daySchedules) {
                $results[$day] = $this->createSchedulesForDay($classroomId, $academicYearId, $day, $daySchedules);
            }
        } elseif ($this->template_type === 'daily') {
            // For daily templates, apply to all days
            $days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
            foreach ($days as $day) {
                $results[$day] = $this->createSchedulesForDay($classroomId, $academicYearId, $day, $templateData);
            }
        }

        return $results;
    }

    /**
     * Create schedules for a specific day based on template
     */
    private function createSchedulesForDay(int $classroomId, int $academicYearId, string $day, array $daySchedules): array
    {
        $createdSchedules = [];

        foreach ($daySchedules as $scheduleData) {
            // Find matching teacher assignment
            $teacherAssignment = TeacherAssignment::where('classroom_id', $classroomId)
                ->where('academic_year_id', $academicYearId)
                ->where('subject_id', $scheduleData['subject_id'])
                ->first();

            if (!$teacherAssignment) {
                continue;
            }

            // Check if schedule already exists
            $existingSchedule = ClassSchedule::where('teacher_assignment_id', $teacherAssignment->id)
                ->where('lesson_hour_id', $scheduleData['lesson_hour_id'])
                ->where('day_of_week', $day)
                ->first();

            if ($existingSchedule) {
                continue;
            }

            // Create new schedule
            $schedule = ClassSchedule::create([
                'teacher_assignment_id' => $teacherAssignment->id,
                'lesson_hour_id' => $scheduleData['lesson_hour_id'],
                'day_of_week' => $day,
                'status' => 'active',
                'notes' => $scheduleData['notes'] ?? null,
            ]);

            $createdSchedules[] = $schedule;
        }

        return $createdSchedules;
    }

    /**
     * Create template from existing classroom schedule
     */
    public static function createFromClassroom(
        int $classroomId,
        int $academicYearId,
        string $name,
        string $description = null,
        int $createdBy = null
    ): self {
        // Get classroom details
        $classroom = Classroom::with(['program', 'shift'])->find($classroomId);
        
        // Get existing schedules
        $schedules = ClassSchedule::with(['teacherAssignment.subject', 'lessonHour'])
            ->whereHas('teacherAssignment', function ($query) use ($classroomId, $academicYearId) {
                $query->where('classroom_id', $classroomId)
                      ->where('academic_year_id', $academicYearId);
            })
            ->where('status', 'active')
            ->get();

        // Group schedules by day
        $templateData = [];
        foreach ($schedules as $schedule) {
            $day = $schedule->day_of_week;
            if (!isset($templateData[$day])) {
                $templateData[$day] = [];
            }

            $templateData[$day][] = [
                'subject_id' => $schedule->teacherAssignment->subject_id,
                'lesson_hour_id' => $schedule->lesson_hour_id,
                'notes' => $schedule->notes,
            ];
        }

        return self::create([
            'name' => $name,
            'description' => $description,
            'program_id' => $classroom->program_id,
            'shift_id' => $classroom->shift_id,
            'template_type' => 'weekly',
            'template_data' => $templateData,
            'is_active' => true,
            'created_by' => $createdBy ?? auth()->id(),
        ]);
    }
}
