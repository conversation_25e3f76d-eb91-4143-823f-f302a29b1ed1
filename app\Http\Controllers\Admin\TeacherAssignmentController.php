<?php

namespace App\Http\Controllers\Admin;

use App\Http\Requests\TeacherAssignmentRequests\{
    TeacherAssignmentStoreRequest,
    TeacherAssignmentFilterRequest,
    TeacherAssignmentUpdateRequest
};
use App\Models\{TeacherAssignment, Teacher, Subject, Classroom, AcademicYear};
use Illuminate\Http\JsonResponse;
use Illuminate\Contracts\View\View;
use App\Http\Controllers\Controller;

class TeacherAssignmentController extends Controller
{
    public function index(TeacherAssignmentFilterRequest $request): View|JsonResponse
    {
        $query = TeacherAssignment::query()
            ->select([
                'teacher_assignments.id', // Explicitly specify the table for 'id'
                'teacher_assignments.teacher_id',
                'teacher_assignments.subject_id',
                'teacher_assignments.classroom_id',
                'teacher_assignments.academic_year_id',
                'teacher_assignments.is_homeroom_teacher',
                'teacher_assignments.created_at'
            ])
            ->with(['teacher.user', 'subject', 'classroom', 'academicYear'])
            ->when($request->validated('teacher_id'), fn($q, $value) => $q->where('teacher_assignments.teacher_id', $value))
            ->when($request->validated('subject_id'), fn($q, $value) => $q->where('teacher_assignments.subject_id', $value))
            ->when($request->validated('classroom_id'), fn($q, $value) => $q->where('teacher_assignments.classroom_id', $value))
            ->when($request->validated('academic_year_id'), fn($q, $value) => $q->where('teacher_assignments.academic_year_id', $value))
            ->when(
                $request->has('is_homeroom_teacher') && $request->validated('is_homeroom_teacher') !== '',
                fn($q) => $q->where('teacher_assignments.is_homeroom_teacher', (bool) $request->validated('is_homeroom_teacher'))
            )
            ->when($request->validated('status'), fn($q, $value) => $q->where('teacher_assignments.status', $value))
            ->when($request->validated('search'), function ($q, $search) {
                $q->where(function ($q) use ($search) {
                    $q->whereHas('teacher.user', fn($userQuery) => $userQuery->where('users.name', 'like', "%{$search}%"))
                        ->orWhereHas('subject', fn($subjectQuery) => $subjectQuery->where('subjects.name', 'like', "%{$search}%"))
                        ->orWhereHas('classroom', fn($classroomQuery) => $classroomQuery->where('classrooms.name', 'like', "%{$search}%"));
                });
            });

        return $request->ajax()
            ? $this->formatForDataTable($query)
            : view('admin.pages.teacher-assignment.index', [
                'initialFilters' => $request->validated(),
            ]);
    }

    private function formatForDataTable($query): JsonResponse
    {
        return datatables()
            ->eloquent($query)
            ->addIndexColumn()
            ->editColumn('teacher.user.name', fn($row) => $row->teacher?->user?->name ?? '-')
            ->editColumn('teacher.user.email', fn($row) => $row->teacher?->user?->email ?? '-')
            ->editColumn('subject.name', fn($row) => $row->subject?->name ?? '-')
            ->editColumn('classroom.name', fn($row) => $row->classroom?->name ?? '-')
            ->editColumn('academicYear.name', fn($row) => $row->academicYear?->name ?? '-')
            ->editColumn('assignment_type', fn($row) => $row->is_homeroom_teacher ? 'Wali Kelas' : 'Guru Mapel')
            ->addColumn('action', fn($row) => view('admin.pages.teacher-assignment._action', [
                'edit' => route('admin.teacher-assignments.edit', $row->id),
                'destroy' => route('admin.teacher-assignments.destroy', $row->id),
                'id' => $row->id,
            ])->render())
            ->rawColumns(['action'])
            ->make(true);
    }

    public function create(): View
    {
        return view('admin.pages.teacher-assignment.create', [
            'teachers' => Teacher::with('user:id,name')
                ->whereHas('user', fn($q) => $q->where('status', 'active'))
                ->get(),
            'subjects' => Subject::select('id', 'name')->orderBy('name')->get(),
            'classrooms' => Classroom::select('id', 'name', 'level')
                ->where('status', 'active')
                ->orderBy('name')
                ->get(),
            'academicYears' => AcademicYear::select('id', 'name', 'semester')
                ->whereIn('status', ['active', 'planned'])
                ->orderBy('name')
                ->get(),
        ]);
    }

    public function store(TeacherAssignmentStoreRequest $request): JsonResponse
    {
        $teacherAssignment = TeacherAssignment::create($request->validated());

        return response()->json([
            'success' => true,
            'message' => 'Penugasan guru berhasil dibuat.',
            'data' => $teacherAssignment,
        ], 201);
    }

    public function edit(TeacherAssignment $teacherAssignment): View
    {
        return view('admin.pages.teacher-assignment.edit', [
            'teacherAssignment' => $teacherAssignment->load([
                'teacher.user:id,name',
                'subject:id,name',
                'classroom:id,name,level',
                'academicYear:id,name,semester'
            ]),
            'teachers' => Teacher::with('user:id,name')
                ->whereHas('user', fn($q) => $q->where('status', 'active'))
                ->get(),
            'subjects' => Subject::select('id', 'name')->orderBy('name')->get(),
            'classrooms' => Classroom::select('id', 'name', 'level')
                ->where('status', 'active')
                ->orderBy('name')
                ->get(),
            'academicYears' => AcademicYear::select('id', 'name', 'semester')
                ->whereIn('status', ['active', 'planned'])
                ->orderBy('name')
                ->get(),
        ]);
    }

    public function update(TeacherAssignmentUpdateRequest $request, TeacherAssignment $teacherAssignment): JsonResponse
    {
        $teacherAssignment->update($request->validated());

        return response()->json([
            'success' => true,
            'message' => 'Penugasan guru berhasil diperbarui.',
        ]);
    }

    public function destroy(TeacherAssignment $teacherAssignment): JsonResponse
    {
        $teacherAssignment->delete();

        return response()->json([
            'success' => true,
            'message' => 'Penugasan guru berhasil dihapus.',
        ]);
    }
}
