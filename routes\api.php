<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\ClassScheduleApiController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Class Schedule API Routes
Route::middleware(['web', 'auth'])->prefix('class-schedules')->name('class-schedules.')->group(function () {
    Route::get('/management-data', [ClassScheduleApiController::class, 'getClassManagementData'])->name('management-data');
    Route::get('/teachers', [ClassScheduleApiController::class, 'getTeachers'])->name('teachers');
    Route::get('/subjects', [ClassScheduleApiController::class, 'getSubjects'])->name('subjects');
    Route::get('/lesson-hours', [ClassScheduleApiController::class, 'getLessonHours'])->name('lesson-hours');
    Route::get('/teacher-assignments', [ClassScheduleApiController::class, 'getTeacherAssignments'])->name('teacher-assignments');
    Route::get('/{classSchedule}/edit-data', [ClassScheduleApiController::class, 'getEditData'])->name('edit-data');

    // Enhanced scheduling endpoints
    Route::post('/check-conflicts', [ClassScheduleApiController::class, 'checkConflicts'])->name('check-conflicts');
    Route::post('/recommendations', [ClassScheduleApiController::class, 'getRecommendations'])->name('recommendations');
    Route::post('/bulk-store', [ClassScheduleApiController::class, 'bulkStore'])->name('bulk-store');
    Route::post('/copy-schedules', [ClassScheduleApiController::class, 'copySchedules'])->name('copy-schedules');
    Route::post('/auto-generate', [ClassScheduleApiController::class, 'autoGenerate'])->name('auto-generate');

    // Template endpoints
    Route::get('/templates', [ClassScheduleApiController::class, 'getTemplates'])->name('templates');
    Route::post('/templates', [ClassScheduleApiController::class, 'createTemplate'])->name('templates.create');
    Route::post('/templates/apply', [ClassScheduleApiController::class, 'applyTemplate'])->name('templates.apply');

    Route::post('/', [ClassScheduleApiController::class, 'store'])->name('store');
    Route::put('/{classSchedule}', [ClassScheduleApiController::class, 'update'])->name('update');
    Route::delete('/{classSchedule}', [ClassScheduleApiController::class, 'destroy'])->name('destroy');
});
