<?php

namespace App\Http\Controllers\Admin;

use App\Models\Shift;
use App\Models\Program;
use App\Models\Teacher;
use App\Enums\StatusEnum;
use App\Enums\UserStatus;
use App\Models\Classroom;
use App\Models\AcademicYear;
use App\Enums\ClassroomLevelEnum;
use Illuminate\Http\JsonResponse;
use Illuminate\Contracts\View\View;
use App\Http\Controllers\Controller;
use App\Enums\AcademicYearStatusEnum;
use Symfony\Component\HttpFoundation\Response;
use App\Http\Requests\ClassroomRequests\ClassroomStoreRequest;
use App\Http\Requests\ClassroomRequests\ClassroomFilterRequest;
use App\Http\Requests\ClassroomRequests\ClassroomUpdateRequest;

class ClassroomController extends Controller
{
    /**
     * Display a listing of classrooms.
     */
    public function index(ClassroomFilterRequest $request): View|JsonResponse
    {
        $query = Classroom::with(['teacher.user', 'program', 'shift', 'academicYear'])
            ->select(['id', 'name', 'level', 'capacity', 'program_id', 'shift_id', 'teacher_id', 'academic_year_id', 'status', 'created_at']);

        $query = $this->applyFilters($query, $request->validated());

        if ($request->ajax()) {
            return $this->formatClassroomForDatatable($query);
        }

        return view('admin.pages.classroom.index', [
            'levels' => ClassroomLevelEnum::options(),
            'statuses' => StatusEnum::options(),
            'programs' => Program::where('status', 'active')->get(),
            'initialFilters' => $request->validated(), // Pass filters to view
        ]);
    }

    /**
     * Apply filters to the query.
     */
    protected function applyFilters($query, array $filters)
    {
        // Filter by program
        if (!empty($filters['program_id'])) {
            $query->where('program_id', $filters['program_id']);
        }

        // Filter by level
        if (!empty($filters['level'])) {
            $query->where('level', $filters['level']);
        }

        // Filter by status
        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        // Search filter
        if (!empty($filters['search'])) {
            $searchTerm = '%' . $filters['search'] . '%';
            $query->where(function ($q) use ($searchTerm) {
                $q->where('name', 'like', $searchTerm)
                    ->orWhereHas('program', function ($q) use ($searchTerm) {
                        $q->where('name', 'like', $searchTerm);
                    })
                    ->orWhereHas('teacher.user', function ($q) use ($searchTerm) {
                        $q->where('name', 'like', $searchTerm);
                    });
            });
        }

        // Default sorting
        $query->orderBy('created_at', 'desc');

        return $query;
    }

    /**
     * Format response for DataTables.
     */
    protected function formatClassroomForDatatable($query): JsonResponse
    {
        return datatables()
            ->eloquent($query)
            ->addIndexColumn()

            // Nama kelas
            ->editColumn('name', fn($row) => $row->name ?? '-')

            // Tingkat kelas (VII, VIII, IX, dst)
            ->editColumn(
                'level',
                fn($row) =>
                ClassroomLevelEnum::getLabel($row->level)
            )

            // Nama program
            ->editColumn(
                'program_name',
                fn($row) =>
                $row->program?->name ?? '<span class="text-muted">-</span>'
            )

            // Nama shift
            ->editColumn(
                'shift_name',
                fn($row) =>
                $row->shift?->name ?? '<span class="text-muted">-</span>'
            )

            // Nama guru
            ->editColumn(
                'teacher_name',
                fn($row) =>
                $row->teacher?->user?->name ?? '<span class="text-muted">-</span>'
            )

            // Tahun akademik dan semester
            ->editColumn('academic_year', function ($row) {
                if (!$row->academicYear) {
                    return '<span class="text-muted">-</span>';
                }

                return "{$row->academicYear->name} - {$row->academicYear->semester?->label()}";
            })

            // Kapasitas
            ->editColumn('capacity_status', function ($row) {
                $current = $row->currentStudents()->count();
                return "<span class='badge bg-info-subtle text-info'>{$current}/{$row->capacity}</span>";
            })

            // Status (aktif/nonaktif)
            ->editColumn('status', function ($row) {
                return "<span class='badge bg-{$row->status->color()}'>{$row->status->label()}</span>";
            })

            // Tombol aksi
            ->addColumn('action', function ($row) {
                return view('admin.pages.classroom._action', [
                    'edit' => route('admin.classrooms.edit', $row->id),
                    'destroy' => route('admin.classrooms.destroy', $row->id),
                    'id' => $row->id,
                ])->render();
            })

            // Kolom yang mengandung HTML
            ->rawColumns([
                'level',
                'program_name',
                'shift_name',
                'teacher_name',
                'academic_year',
                'capacity_status',
                'status',
                'action',
            ])
            ->make(true);
    }

    /**
     * Show the form for creating a new classroom.
     */
    public function create(): View
    {
        $programs = Program::where('status', 'active')->get();
        $teachers = Teacher::whereHas('user', function ($q) {
            $q->where('status', UserStatus::Active);
        })->get();
        $academicYears = AcademicYear::where('status', AcademicYearStatusEnum::ACTIVE)->get();
        $shifts = Shift::where('status', 'active')->get();
        $levels = ClassroomLevelEnum::options();
        return view('admin.pages.classroom.create', [
            'programs' => $programs,
            'teachers' => $teachers,
            'academicYears' => $academicYears,
            'shifts' => $shifts,
            'levels' => $levels,
            'statuses' => StatusEnum::options(),
        ]);
    }

    /**
     * Store a newly created classroom in storage.
     */
    public function store(ClassroomStoreRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $classroom = Classroom::create($validated);

        return response()->json([
            'success' => true,
            'message' => 'Kelas berhasil dibuat.',
            'data' => $classroom,
        ], Response::HTTP_CREATED);
    }

    /**
     * Display the specified classroom.
     *
     * @param  int  $id
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified classroom.
     *
     * @param  int  $id
     */
    public function edit($id): View
    {
        $classroom = Classroom::findOrFail($id);
        $programs = Program::where('status', 'active')->get();
        $teachers = Teacher::whereHas('user', function ($q) {
            $q->where('status', UserStatus::Active);
        })->get();
        $academicYears = AcademicYear::where('status', AcademicYearStatusEnum::ACTIVE)->get();
        $shifts = Shift::where('status', 'active')->get();
        return view('admin.pages.classroom.edit', [
            'classroom' => $classroom,
            'programs' => $programs,
            'teachers' => $teachers,
            'academicYears' => $academicYears,
            'shifts' => $shifts,
            'levels' => ClassroomLevelEnum::options(),
            'statuses' => StatusEnum::options(),
        ]);
    }

    /**
     * Update the specified classroom in storage.
     */
    public function update(ClassroomUpdateRequest $request, int $id): JsonResponse
    {
        $classroom = Classroom::findOrFail($id);
        $validated = $request->validated();

        $classroom->update($validated);

        return response()->json([
            'success' => true,
            'message' => 'Kelas berhasil diperbarui.',
        ]);
    }

    /**
     * Remove the specified classroom from storage.
     */
    public function destroy(int $id): JsonResponse
    {
        $classroom = Classroom::findOrFail($id);

        // Prevent deleting classroom with students
        if ($classroom->currentStudents()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => 'Tidak dapat menghapus kelas yang masih memiliki siswa.',
            ], Response::HTTP_FORBIDDEN);
        }

        // Prevent deleting classroom with active schedules
        if ($classroom->lessonHours()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => 'Tidak dapat menghapus kelas yang masih memiliki jadwal pelajaran.',
            ], Response::HTTP_FORBIDDEN);
        }

        $classroom->delete();

        return response()->json([
            'success' => true,
            'message' => 'Kelas berhasil dihapus.',
        ]);
    }
}
