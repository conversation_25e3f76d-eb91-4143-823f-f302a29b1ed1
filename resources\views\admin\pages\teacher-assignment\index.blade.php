@extends('admin.layouts.app')

@section('title', 'Penugasan Guru')

@section('content')
    @include('admin.components.page-title', [
        'title' => 'Penugasan Guru',
        'breadcrumb' => 'Manaj<PERSON>en Akademik',
    ])

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <!-- Card Header -->
                <div class="card-header border-bottom-dashed">
                    <div class="d-flex flex-column flex-sm-row justify-content-between align-items-center gap-3">
                        <!-- Title with Counter -->
                        <div>
                            <h5 class="card-title mb-0 d-flex align-items-center">
                                Daftar @yield('title')
                                <span class="badge bg-primary-subtle text-primary ms-2" id="total-assignments">0</span>
                            </h5>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-outline-primary" id="export-btn">
                                <i class="ri-file-download-line align-bottom"></i> Export
                            </button>
                            <button type="button" class="btn btn-outline-success" id="import-btn">
                                <i class="ri-upload-line align-bottom"></i> Import
                            </button>
                            <a href="{{ route('admin.teacher-assignments.create') }}" class="btn btn-primary">
                                <i class="ri-add-line align-bottom"></i> Tambah Baru
                            </a>
                        </div>
                    </div>
                </div>
                <!-- End Card Header -->

                <!-- Filter Section -->
                <div class="card-body border-bottom-dashed border-bottom">
                    <form id="filter-form" class="row g-3">
                        <!-- Assignment Type Dropdown -->
                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="filter-assignment-type" class="form-label">Jenis Penugasan</label>
                                <select class="form-select" data-choices name="is_homeroom_teacher" id="filter-assignment-type">
                                    <option value="">Semua Jenis</option>
                                    <option value="1">Wali Kelas</option>
                                    <option value="0">Guru Mapel</option>
                                </select>
                            </div>
                        </div>



                        <!-- Search Input -->
                        <div class="col-md-5">
                            <div class="form-group">
                                <label for="search-input" class="form-label">Cari</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" placeholder="Cari guru, mata pelajaran, atau kelas..." id="search-input" name="search">
                                    <button class="btn btn-primary" type="button" id="search-button">
                                        <i class="ri-search-line"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Clear Filter Button -->
                        <div class="col-md-3">
                            <div class="form-group">
                                <label class="form-label">&nbsp;</label>
                                <button type="button" class="btn btn-outline-secondary w-100" id="clear-filter">
                                    <i class="ri-refresh-line align-bottom"></i> Reset Filter
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
                <!-- End Filter Section -->

                <!-- Table Section -->
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="teacher-assignment-table" class="table nowrap align-middle" style="width:100%">
                            <thead class="table-light text-muted">
                                <tr class="text-uppercase">
                                    <th>No</th>
                                    <th>Nama Guru</th>
                                    <th>Mata Pelajaran</th>
                                    <th>Kelas</th>
                                    <th>Tahun Akademik</th>
                                    <th>Jenis Penugasan</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody class="list">
                                <!-- Data akan diisi via AJAX -->
                            </tbody>
                        </table>
                    </div>
                </div>
                <!-- End Table Section -->
            </div>
        </div>
    </div>
@endsection

@push('styles')
    <style type="text/css">
        .dataTables_length,
        .dataTables_filter {
            display: none !important;
        }
    </style>
@endpush

@include('admin.partials.plugins.jquery')
@include('admin.partials.plugins.datatables')
@include('admin.partials.plugins.sweetalert')

@push('scripts')
    <script type="text/javascript">
        $(document).ready(function() {
            // Inisialisasi DataTable
            const teacherAssignmentTable = $('#teacher-assignment-table').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: "{{ route('admin.teacher-assignments.index') }}",
                    data: d => {
                        d.is_homeroom_teacher = $('#filter-assignment-type').val();
                        d.search = $('#search-input').val();
                    },
                    complete: response => {
                        const totalAssignments = response.responseJSON?.recordsTotal || 0;
                        $('#total-assignments').text(totalAssignments);
                    },
                    error: xhr => {
                        const message = xhr.responseJSON?.message || 'Gagal memuat data penugasan guru.';
                        Swal.fire({
                            title: 'Error!',
                            text: message,
                            icon: 'error'
                        });
                    }
                },
                columns: [{
                    data: 'DT_RowIndex',
                    name: 'DT_RowIndex',
                    orderable: false,
                    searchable: false,
                }, {
                    data: 'teacher.user.name',
                    name: 'teacher.user.name',
                }, {
                    data: 'subject.name',
                    name: 'subject.name',
                }, {
                    data: 'classroom.name',
                    name: 'classroom.name',
                }, {
                    data: 'academicYear.name',
                    name: 'academicYear.name',
                }, {
                    data: 'assignment_type',
                    name: 'assignment_type',
                    orderable: false,
                }, {
                    data: 'action',
                    name: 'action',
                    orderable: false,
                    searchable: false,
                }],
                language: {
                    processing: '<div class="spinner-border text-primary m-1" role="status"></div>',
                    searchPlaceholder: 'Cari...',
                    lengthMenu: 'Tampilkan _MENU_ data',
                    zeroRecords: 'Data tidak ditemukan',
                    info: 'Menampilkan _START_ sampai _END_ dari _TOTAL_ data',
                    infoEmpty: 'Menampilkan 0 sampai 0 dari 0 data',
                    infoFiltered: '(disaring dari _MAX_ total data)',
                    paginate: {
                        first: 'Pertama',
                        last: 'Terakhir',
                        next: 'Selanjutnya',
                        previous: 'Sebelumnya',
                    }
                },
                order: [
                    [1, 'asc'], // Sort by teacher name by default
                ] // Sort by teacher name by default
            });

            // Fungsi untuk memuat ulang tabel
            const reloadTable = () => teacherAssignmentTable.draw();

            // Fungsi untuk menampilkan pesan "Coming Soon"
            const showComingSoon = () => {
                Swal.fire({
                    title: 'Coming Soon!',
                    text: 'Fitur ini akan segera tersedia.',
                    icon: 'info',
                });
            };

            // Fungsi untuk menangani penghapusan data
            const handleDelete = url => {
                Swal.fire({
                    title: 'Konfirmasi Hapus',
                    text: 'Anda yakin ingin menghapus penugasan guru ini?',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'Ya, Hapus!',
                    cancelButtonText: 'Batal',
                    reverseButtons: true,
                }).then(result => {
                    if (result.isConfirmed) {
                        $.ajax({
                            url,
                            type: 'DELETE',
                            headers: {
                                'X-CSRF-TOKEN': '{{ csrf_token() }}'
                            },
                            success: response => {
                                Swal.fire({
                                    title: 'Berhasil!',
                                    text: response.message,
                                    icon: 'success',
                                    showConfirmButton: false,
                                    timer: 1500,
                                });
                                reloadTable();
                            },
                            error: xhr => {
                                const message = xhr.responseJSON?.message || 'Terjadi kesalahan saat menghapus data.';
                                Swal.fire({
                                    title: 'Error!',
                                    text: message,
                                    icon: 'error',
                                });
                            }
                        });
                    }
                });
            };

            // Event Listeners
            $('#topnav-hamburger-icon').click(() => {
                setTimeout(() => {
                    teacherAssignmentTable.columns.adjust().draw();
                }, 300);
            });

            $('#filter-assignment-type').change(reloadTable);
            $('#search-button').click(reloadTable);
            $('#search-input').keyup(e => e.key === 'Enter' && reloadTable());

            $('#export-btn, #import-btn').click(showComingSoon);

            $('#teacher-assignment-table').on('click', '.btn-delete-item', function() {
                const url = $(this).data('url');
                if (!url) {
                    Swal.fire({
                        title: 'Error!',
                        text: 'URL hapus tidak ditemukan.',
                        icon: 'error',
                    });
                    return;
                }
                handleDelete(url);
            });
        });
    </script>
@endpush
